"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[2365],{2365:function(r,e,n){n.r(e),n.d(e,{default:function(){return k}});var t=n(641);const c={class:"error-page"},o={class:"error-content"};function a(r,e,n,a,u,l){const s=(0,t.g2)("el-button");return(0,t.uX)(),(0,t.CE)("div",c,[(0,t.Lk)("div",o,[e[1]||(e[1]=(0,t.Lk)("h1",null,"401",-1)),e[2]||(e[2]=(0,t.Lk)("h2",null,"未授权访问",-1)),e[3]||(e[3]=(0,t.Lk)("p",null,"抱歉，您没有权限访问此页面",-1)),(0,t.bF)(s,{type:"primary",onClick:l.goBack},{default:(0,t.k6)(()=>e[0]||(e[0]=[(0,t.eW)("返回",-1)])),_:1,__:[0]},8,["onClick"])])])}var u={name:"Error401",methods:{goBack(){this.$router.go(-1)}}},l=n(6262);const s=(0,l.A)(u,[["render",a],["__scopeId","data-v-61e56274"]]);var k=s}}]);
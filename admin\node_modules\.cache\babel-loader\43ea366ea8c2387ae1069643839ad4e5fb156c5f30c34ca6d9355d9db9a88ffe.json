{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default function modalIcon(callback) {\n  var _this = this;\n  var h = this.$createElement;\n  return new Promise(function (resolve, reject) {\n    _this.$msgbox({\n      title: '菜单图标',\n      customClass: 'upload-form',\n      closeOnClickModal: false,\n      showClose: true,\n      message: h('div', {\n        class: 'common-form-upload'\n      }, [h('iconFrom', {\n        on: {\n          getIcon: function getIcon(n) {\n            callback(n);\n          }\n        }\n      })]),\n      showCancelButton: false,\n      showConfirmButton: false\n    }).then(function () {\n      resolve();\n    }).catch(function () {\n      reject();\n      _this.$message({\n        type: 'info',\n        message: '已取消'\n      });\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
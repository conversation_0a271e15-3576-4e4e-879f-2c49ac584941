{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport hasRole from './permission/hasRole';\nimport hasPermi from './permission/hasPermi';\nimport dialogDrag from './dialog/drag';\nimport dialogDragWidth from './dialog/dragWidth';\nimport dialogDragHeight from './dialog/dragHeight';\nimport copy from './copy/copy';\nvar install = function install(Vue) {\n  Vue.directive('hasRole', hasRole);\n  Vue.directive('hasPermi', hasPermi);\n  Vue.directive('dialogDrag', dialogDrag);\n  Vue.directive('dialogDragWidth', dialogDragWidth);\n  Vue.directive('dialogDragHeight', dialogDragHeight);\n  Vue.directive('copy', copy);\n};\nif (window.Vue) {\n  window['hasRole'] = hasRole;\n  window['hasPermi'] = hasPermi;\n  Vue.use(install); // eslint-disable-line\n}\nexport default install;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
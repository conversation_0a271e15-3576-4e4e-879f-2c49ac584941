{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n// 导出所有 Pinia stores\nexport { useAppStore } from '../stores/app';\nexport { useUserStore } from '../stores/user';\nexport { usePermissionStore } from '../stores/permission';\n\n// 为了兼容性，保留一些 getters\nexport const getters = {\n  sidebar: () => {\n    const appStore = useAppStore();\n    return appStore.sidebar;\n  },\n  size: () => {\n    const appStore = useAppStore();\n    return appStore.size;\n  },\n  device: () => {\n    const appStore = useAppStore();\n    return appStore.device;\n  },\n  token: () => {\n    const userStore = useUserStore();\n    return userStore.token;\n  },\n  avatar: () => {\n    const userStore = useUserStore();\n    return userStore.avatar;\n  },\n  name: () => {\n    const userStore = useUserStore();\n    return userStore.name;\n  },\n  roles: () => {\n    const userStore = useUserStore();\n    return userStore.roles;\n  },\n  permissions: () => {\n    const userStore = useUserStore();\n    return userStore.permissions;\n  },\n  isLogin: () => {\n    const userStore = useUserStore();\n    return userStore.isLogin;\n  },\n  permission_routes: () => {\n    const permissionStore = usePermissionStore();\n    return permissionStore.routes;\n  },\n  sidebarRouters: () => {\n    const permissionStore = usePermissionStore();\n    return permissionStore.sidebarRouters;\n  }\n};\n\n// 默认导出 getters 以保持兼容性\nexport default getters;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.number.to-fixed.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.replace.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n// import parseTime, formatTime and set to filter\nexport { parseTime, formatTime } from '@/utils';\nexport * from '../filters/commFilter';\nexport * from '../filters/user';\nexport * from '../filters/order';\nexport * from '../filters/wx';\n\n/**\r\n * Show plural label if time is plural number\r\n * @param {number} time\r\n * @param {string} label\r\n * @return {string}\r\n */\nfunction pluralize(time, label) {\n  if (time === 1) {\n    return time + label;\n  }\n  return time + label + 's';\n}\n\n/**\r\n * @param {number} time\r\n */\nexport function timeAgo(time) {\n  var between = Date.now() / 1000 - Number(time);\n  if (between < 3600) {\n    return pluralize(~~(between / 60), ' minute');\n  } else if (between < 86400) {\n    return pluralize(~~(between / 3600), ' hour');\n  } else {\n    return pluralize(~~(between / 86400), ' day');\n  }\n}\n\n/**\r\n * Number formatting\r\n * like 10000 => 10k\r\n * @param {number} num\r\n * @param {number} digits\r\n */\nexport function numberFormatter(num, digits) {\n  var si = [{\n    value: 1e18,\n    symbol: 'E'\n  }, {\n    value: 1e15,\n    symbol: 'P'\n  }, {\n    value: 1e12,\n    symbol: 'T'\n  }, {\n    value: 1e9,\n    symbol: 'G'\n  }, {\n    value: 1e6,\n    symbol: 'M'\n  }, {\n    value: 1e3,\n    symbol: 'k'\n  }];\n  for (var i = 0; i < si.length; i++) {\n    if (num >= si[i].value) {\n      return (num / si[i].value).toFixed(digits).replace(/\\.0+$|(\\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol;\n    }\n  }\n  return num.toString();\n}\n\n/**\r\n * 10000 => \"10,000\"\r\n * @param {number} num\r\n */\nexport function toThousandFilter(num) {\n  return (+num || 0).toString().replace(/^-?\\d+/g, function (m) {\n    return m.replace(/(?=(?!\\b)(\\d{3})+$)/g, ',');\n  });\n}\n\n/**\r\n * Upper case first char\r\n * @param {String} string\r\n */\nexport function uppercaseFirst(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
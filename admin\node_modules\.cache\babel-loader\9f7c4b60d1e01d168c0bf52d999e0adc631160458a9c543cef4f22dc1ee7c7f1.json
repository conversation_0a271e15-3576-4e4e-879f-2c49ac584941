{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia';\nimport { asyncRoutes, constantRoutes } from '@/router';\nimport * as roleApi from '@/api/roleApi.js';\nimport * as Auth from '@/libs/wechat';\nimport { formatRoutes } from '@/utils/parsing';\n\n/**\n * Filter asynchronous routing tables by recursion\n * @param routes asyncRoutes\n * @param roles\n */\nexport function filterAsyncRoutes(routes, roles) {\n  const res = [];\n  routes.forEach(route => {\n    const tmp = {\n      ...route\n    };\n    if (tmp.child) {\n      tmp.child = filterAsyncRoutes(tmp.child, roles);\n    }\n    res.push(tmp);\n  });\n  return res;\n}\nfunction comRouter(menus, asyncRouter, hasLeft) {\n  let router = [];\n  asyncRouter.forEach(item => {\n    if (menus.indexOf(item.name) !== -1 || hasLeft.indexOf(item.name) !== -1) {\n      if (item.children && item.children.length) {\n        item.children = comRouter(menus, item.children, hasLeft);\n      }\n      router.push(item);\n    }\n  });\n  return router;\n}\nexport const usePermissionStore = defineStore('permission', {\n  state: () => ({\n    routes: [],\n    addRoutes: [],\n    topbarRouters: [],\n    sidebarRouters: []\n  }),\n  getters: {\n    permissionRoutes: state => state.routes,\n    sidebarRoutes: state => state.sidebarRouters,\n    topbarRoutes: state => state.topbarRouters\n  },\n  actions: {\n    // 生成路由\n    async generateRoutes(roleid) {\n      return new Promise(async resolve => {\n        let accessedRoutes = [];\n        let menus = [];\n        let hasLeft = [];\n        try {\n          const res = await roleApi.getMenus();\n          if (res.data) {\n            menus = formatRoutes(res.data);\n            hasLeft = res.data.map(item => item.menu_name);\n          }\n        } catch (error) {\n          console.error('获取菜单失败:', error);\n        }\n        accessedRoutes = comRouter(menus, constantRoutes, hasLeft);\n        this.addRoutes = accessedRoutes;\n        this.routes = accessedRoutes;\n        this.sidebarRouters = accessedRoutes;\n        this.topbarRouters = accessedRoutes;\n        resolve(accessedRoutes);\n      });\n    },\n    // 设置路由\n    setRoutes(routes) {\n      this.addRoutes = routes;\n      this.routes = routes;\n    },\n    // 设置顶部路由\n    setTopbarRoutes(routes) {\n      this.topbarRouters = routes;\n    },\n    // 设置侧边栏路由\n    setSidebarRouters(routes) {\n      this.sidebarRouters = routes;\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
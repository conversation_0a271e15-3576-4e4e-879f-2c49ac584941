{"ast": null, "code": "import \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from '@/store';\n\n/**\r\n * 字符权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\nexport function checkPermi(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    var permissions = store.getters && store.getters.permissions;\n    var permissionDatas = value;\n    var all_permission = '*:*:*';\n    var hasPermission = permissions.some(function (permission) {\n      return all_permission === permission || permissionDatas.includes(permission);\n    });\n    if (!hasPermission) {\n      return false;\n    }\n    return true;\n  } else {\n    console.error(\"need roles! Like checkPermi=\\\"['system:user:add','system:user:edit']\\\"\");\n    return false;\n  }\n}\n\n/**\r\n * 角色权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\nexport function checkRole(value) {\n  if (value && value instanceof Array && value.length > 0) {\n    var roles = store.getters && store.getters.roles;\n    var permissionRoles = value;\n    var super_admin = 'admin';\n    var hasRole = roles.some(function (role) {\n      return super_admin === role || permissionRoles.includes(role);\n    });\n    if (!hasRole) {\n      return false;\n    }\n    return true;\n  } else {\n    console.error(\"need roles! Like checkRole=\\\"['admin','editor']\\\"\");\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
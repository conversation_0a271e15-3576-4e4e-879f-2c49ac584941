{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nconst title = localStorage.getItem('singleAdminSiteName') || 'CRMEB Java';\nexport default function getPageTitle(pageTitle) {\n  if (pageTitle) {\n    return `${pageTitle} - ${title}`;\n  }\n  return `${title}`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
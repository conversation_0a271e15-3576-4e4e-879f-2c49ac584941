{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { ElMessageBox, ElMessage, ElNotification, ElLoading } from 'element-plus';\nconst dialog = {\n  confirm: ElMessageBox.confirm,\n  alert: ElMessageBox.alert,\n  toast: ElMessage,\n  notify: ElNotification,\n  loading: ElLoading\n};\nconst icons = {\n  error: '操作失败',\n  success: '操作成功'\n};\nObject.keys(icons).reduce((dialog, key) => {\n  dialog[key] = (mes, obj = {}) => {\n    return new Promise(function (resolve) {\n      ElMessage({\n        message: mes || icons[key],\n        type: key,\n        duration: 1000,\n        onClose: () => {\n          resolve();\n        },\n        ...obj\n      });\n    });\n  };\n  return dialog;\n}, dialog);\ndialog.message = (mes = '操作失败', obj = {}) => {\n  return new Promise(function (resolve) {\n    ElMessage({\n      message: mes,\n      duration: 1000,\n      onClose: () => {\n        resolve();\n      },\n      ...obj\n    });\n  });\n};\ndialog.validateError = (...args) => {\n  validatorDefaultCatch(...args);\n};\nexport function validatorDefaultCatch(err, type = 'message') {\n  console.log(err);\n  return dialog[type](err.errors[0].message);\n}\nexport default dialog;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
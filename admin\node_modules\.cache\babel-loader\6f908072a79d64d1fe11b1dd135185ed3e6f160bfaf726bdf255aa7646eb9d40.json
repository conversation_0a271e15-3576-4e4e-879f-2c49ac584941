{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nvar callbacks = {};\n\n/**\r\n * 加载一个远程脚本\r\n * @param {String} src 一个远程脚本\r\n * @param {Function} callback 回调\r\n */\nfunction loadScript(src, callback) {\n  var existingScript = document.getElementById(src);\n  var cb = callback || function () {};\n  if (!existingScript) {\n    callbacks[src] = [];\n    var $script = document.createElement('script');\n    $script.src = src;\n    $script.id = src;\n    $script.async = 1;\n    document.body.appendChild($script);\n    var onEnd = 'onload' in $script ? stdOnEnd.bind($script) : ieOnEnd.bind($script);\n    onEnd($script);\n  }\n  callbacks[src].push(cb);\n  function stdOnEnd(script) {\n    var _this = this;\n    script.onload = function () {\n      _this.onerror = _this.onload = null;\n      callbacks[src].forEach(function (item) {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n    script.onerror = function () {\n      _this.onerror = _this.onload = null;\n      cb(new Error(\"Failed to load \".concat(src)), script);\n    };\n  }\n  function ieOnEnd(script) {\n    var _this2 = this;\n    script.onreadystatechange = function () {\n      if (_this2.readyState !== 'complete' && _this2.readyState !== 'loaded') return;\n      _this2.onreadystatechange = null;\n      callbacks[src].forEach(function (item) {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n  }\n}\n\n/**\r\n * 顺序加载一组远程脚本\r\n * @param {Array} list 一组远程脚本\r\n * @param {Function} cb 回调\r\n */\nexport function loadScriptQueue(list, cb) {\n  var first = list.shift();\n  list.length ? loadScript(first, function () {\n    return loadScriptQueue(list, cb);\n  }) : loadScript(first, cb);\n}\nexport default loadScript;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
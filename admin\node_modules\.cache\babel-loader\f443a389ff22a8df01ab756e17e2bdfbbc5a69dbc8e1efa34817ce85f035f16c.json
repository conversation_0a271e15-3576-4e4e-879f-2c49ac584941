{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar contentRouter = {\n  path: '/content',\n  component: Layout,\n  redirect: '/content/classifManager',\n  name: 'content',\n  meta: {\n    title: '内容',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'articleManager',\n    name: 'articleManager',\n    component: function component() {\n      return import('@/views/content/article/list');\n    },\n    meta: {\n      title: '文章管理',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'articleCreat/:id?',\n    name: 'articleCreat',\n    component: function component() {\n      return import('@/views/content/article/edit');\n    },\n    meta: {\n      title: '添加文章',\n      noCache: true,\n      activeMenu: \"/content/articleManager\"\n    }\n  }, {\n    path: 'classifManager',\n    name: 'classifManager',\n    component: function component() {\n      return import('@/views/content/articleclass/list');\n    },\n    meta: {\n      title: '文章分类',\n      icon: 'clipboard'\n    }\n  }]\n};\nexport default contentRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
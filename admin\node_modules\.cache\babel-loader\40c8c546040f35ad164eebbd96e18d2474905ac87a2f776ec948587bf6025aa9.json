{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Cookies from 'js-cookie';\nvar TokenKey = 'Authori-zation';\nexport function getToken() {\n  return Cookies.get(TokenKey);\n}\nexport function setToken(token) {\n  return Cookies.set(TokenKey, token);\n}\nexport function removeToken() {\n  return Cookies.remove(TokenKey);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
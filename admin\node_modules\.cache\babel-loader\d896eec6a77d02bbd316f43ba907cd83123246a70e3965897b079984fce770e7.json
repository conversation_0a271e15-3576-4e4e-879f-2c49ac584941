{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n// 请求接口地址 - 小程序版本使用固定地址\nvar VUE_APP_API_URL = process.env.VUE_APP_BASE_API || 'http://localhost:20500/api';\nmodule.exports = {\n  // 接口请求地址\n  apiBaseURL: VUE_APP_API_URL,\n  title: '加载中...',\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether show the settings right-panel\r\n   */\n  showSettings: true,\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether need tagsView\r\n   */\n  tagsView: true,\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether fix the header\r\n   */\n  fixedHeader: true,\n  /**\r\n   * @type {boolean} true | false\r\n   * @description Whether show the logo in sidebar\r\n   */\n  sidebarLogo: true,\n  /**\r\n   * @type {string | array} 'production' | ['production', 'development']\r\n   * @description Need show err logs component.\r\n   * The default is only used in the production env\r\n   * If you want to also use it in dev, you can pass ['production', 'development']\r\n   */\n  errorLog: 'production'\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}
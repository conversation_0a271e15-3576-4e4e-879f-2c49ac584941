{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport store from '@/store';\nfunction authPermission(permission) {\n  const all_permission = '*:*:*';\n  const permissions = store.getters && store.getters.permissions;\n  if (permission && permission.length > 0) {\n    return permissions.some(v => {\n      return all_permission === v || v === permission;\n    });\n  } else {\n    return false;\n  }\n}\nfunction authRole(role) {\n  const super_admin = 'admin';\n  const roles = store.getters && store.getters.roles;\n  if (role && role.length > 0) {\n    return roles.some(v => {\n      return super_admin === v || v === role;\n    });\n  } else {\n    return false;\n  }\n}\nexport default {\n  // 验证用户是否具备某权限\n  hasPermi(permission) {\n    return authPermission(permission);\n  },\n  // 验证用户是否含有指定权限，只需包含其中一个\n  hasPermiOr(permissions) {\n    return permissions.some(item => {\n      return authPermission(item);\n    });\n  },\n  // 验证用户是否含有指定权限，必须全部拥有\n  hasPermiAnd(permissions) {\n    return permissions.every(item => {\n      return authPermission(item);\n    });\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
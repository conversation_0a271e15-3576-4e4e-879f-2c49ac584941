{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nconst financialRouter = {\n  path: '/financial',\n  component: Layout,\n  redirect: '/financial/commission/template',\n  name: 'Financial',\n  meta: {\n    title: '财务',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'commission',\n    component: () => import('@/views/financial/index'),\n    name: 'Commission',\n    meta: {\n      title: '财务操作',\n      icon: ''\n    },\n    alwaysShow: true,\n    children: [{\n      path: 'template',\n      component: () => import('@/views/financial/commission/withdrawal/index'),\n      name: 'commissionTemplate',\n      meta: {\n        title: '申请提现',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'record',\n    component: () => import('@/views/financial/record/index'),\n    name: 'financialRecord',\n    meta: {\n      title: '财务记录',\n      icon: ''\n    },\n    alwaysShow: true,\n    children: [{\n      path: 'charge',\n      component: () => import('@/views/financial/record/charge/index'),\n      name: 'Charge',\n      meta: {\n        title: '充值记录',\n        icon: ''\n      }\n    }, {\n      path: 'monitor',\n      component: () => import('@/views/financial/record/monitor/index'),\n      name: 'Monitor',\n      meta: {\n        title: '资金监控',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'brokerage',\n    component: () => import('@/views/financial/brokerage/index'),\n    name: 'Brokerage',\n    meta: {\n      title: '佣金记录',\n      icon: ''\n    }\n  }]\n};\nexport default financialRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/**\r\n * $prompt一行内容input提交封装\r\n * @param inputType input type 类型\r\n * @param title 标题\r\n * @param label\r\n * @param val 回显的值\r\n * @returns {Promise<any>}\r\n */\nexport default function modalPrompt(inputType, title, val, label) {\n  var _this = this;\n  return new Promise(function (resolve, reject) {\n    _this.$prompt('', \"\".concat(title), {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      inputErrorMessage: \"\\u8BF7\\u8F93\\u5165\".concat(title),\n      inputType: inputType,\n      inputValue: val ? val : '',\n      showClose: true,\n      closeOnClickModal: false,\n      customClass: 'prompt-form',\n      inputPlaceholder: \"\\u8BF7\\u8F93\\u5165\".concat(title),\n      inputValidator: function inputValidator(value) {\n        if (value === null) {\n          return true;\n          //return '输入不能为空';\n        }\n        // if (value.indexOf(' ') !== -1 && !value) return '输入不能为空';\n        //  if (value.indexOf(' ') !== -1 && value === ' ') return '输入不能为空';\n        if (value.length > 50) return '输入限制50字以内';\n      },\n      beforeClose: function beforeClose(action, instance, done) {\n        done();\n      }\n    }).then(function (_ref) {\n      var value = _ref.value;\n      resolve(value);\n    }).catch(function () {\n      _this.$message.info('取消输入');\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Vue from 'vue';\nimport store from '@/store';\nimport { isString, isArray } from '@/utils/validate';\nimport settings from '@/settings';\n\n// you can set in settings.js\n// errorLog:'production' | ['production', 'development']\nconst {\n  errorLog: needErrorLog\n} = settings;\nfunction checkNeed() {\n  const env = process.env.NODE_ENV;\n  if (isString(needErrorLog)) {\n    return env === needErrorLog;\n  }\n  if (isArray(needErrorLog)) {\n    return needErrorLog.includes(env);\n  }\n  return false;\n}\nif (checkNeed()) {\n  Vue.config.errorHandler = function (err, vm, info) {\n    // Don't ask me why I use Vue.nextTick, it just a hack.\n    // detail see https://forum.vuejs.org/t/dispatch-in-vue-config-errorhandler-has-some-problem/23500\n    Vue.nextTick(() => {\n      store.dispatch('errorLog/addErrorLog', {\n        err,\n        vm,\n        info,\n        url: window.location.href\n      });\n    });\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar designRouter = {\n  path: '/design',\n  component: Layout,\n  redirect: '/design/devise',\n  name: 'design',\n  meta: {\n    title: '装修',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'theme',\n    name: 'theme',\n    component: function component() {\n      return import('@/views/design/theme/index');\n    },\n    meta: {\n      title: '一键换色'\n    }\n  }, {\n    path: 'viewDesign',\n    name: 'viewDesign',\n    component: function component() {\n      return import('@/views/design/viewDesign/index');\n    },\n    meta: {\n      title: '页面设计'\n    }\n  }, {\n    path: 'devise',\n    name: 'devise',\n    component: function component() {\n      return import('@/views/design/devise/index');\n    },\n    meta: {\n      title: '首页装修'\n    }\n  }]\n};\nexport default designRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
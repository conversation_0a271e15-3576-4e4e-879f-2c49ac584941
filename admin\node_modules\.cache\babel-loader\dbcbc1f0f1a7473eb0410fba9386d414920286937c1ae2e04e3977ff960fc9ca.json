{"ast": null, "code": "/**\r\n * 一个简单的函数防抖\r\n * @param {Function} fun 需要限制执行频率的函数\r\n * @param {Number} delay 延迟时间，这段时间过后，才可触发第二次\r\n */\nexport default function (fun, delay) {\n  // 记录上一次函数触发的时间\n  var timer = null;\n  var debounced = function () {\n    var ctx = this;\n    var args = arguments;\n    // 清除上一次延时器\n    if (timer) clearTimeout(timer);\n    timer = setTimeout(function () {\n      fun.apply(ctx, args);\n    }, delay);\n  };\n  return debounced;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
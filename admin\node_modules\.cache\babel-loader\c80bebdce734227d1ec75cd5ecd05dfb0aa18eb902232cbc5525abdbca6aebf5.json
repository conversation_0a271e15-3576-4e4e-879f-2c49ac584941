{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\n\n/**\r\n * 优惠券 列表\r\n * @param pram\r\n */\nexport function marketingListApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 用户管理 优惠券列表\r\n * @param pram\r\n */\nexport function marketingSendApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/send/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 优惠券 详情\r\n * @param pram\r\n */\nexport function couponInfoApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/info',\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 优惠券 发送\r\n * @param pram\r\n */\nexport function couponUserApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/user/receive',\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 优惠券 发送\r\n * @param pram\r\n */\nexport function couponSaveApi(data) {\n  return request({\n    url: '/admin/marketing/coupon/save',\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 优惠券 修改状态\r\n * @param pram\r\n */\nexport function couponIssueStatusApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/update/status',\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 优惠券 删除\r\n * @param pram\r\n */\nexport function couponDeleteApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/delete',\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 会员领取记录 列表\r\n * @param pram\r\n */\nexport function couponUserListApi(params) {\n  return request({\n    url: '/admin/marketing/coupon/user/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 积分日志 列表\r\n * @param pram\r\n */\nexport function integralListApi(params, data) {\n  return request({\n    url: '/admin/user/integral/list',\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 秒杀配置 列表\r\n * @param pram\r\n */\nexport function seckillListApi(params) {\n  return request({\n    url: '/admin/store/seckill/manger/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀配置 详情\r\n * @param pram\r\n */\nexport function seckillInfoApi(params) {\n  return request({\n    url: '/admin/store/seckill/manger/info',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀配置 新增\r\n * @param pram\r\n */\nexport function seckillSaveApi(data) {\n  return request({\n    url: '/admin/store/seckill/manger/save',\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 秒杀配置 修改\r\n * @param pram\r\n */\nexport function seckillUpdateApi(params, data) {\n  return request({\n    url: '/admin/store/seckill/manger/update',\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 秒杀配置 删除\r\n * @param pram\r\n */\nexport function seckillDeleteApi(params) {\n  return request({\n    url: '/admin/store/seckill/manger/delete',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀商品 列表\r\n * @param pram\r\n */\nexport function seckillStoreListApi(params) {\n  return request({\n    url: '/admin/store/seckill/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀商品 详情\r\n * @param pram\r\n */\nexport function seckillStoreInfoApi(params) {\n  return request({\n    url: '/admin/store/seckill/info',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀商品 新增\r\n * @param pram\r\n */\nexport function seckillStoreSaveApi(data) {\n  return request({\n    url: '/admin/store/seckill/save',\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 秒杀商品 修改\r\n * @param pram\r\n */\nexport function seckillStoreUpdateApi(params, data) {\n  return request({\n    url: '/admin/store/seckill/update',\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 秒杀商品 删除\r\n * @param pram\r\n */\nexport function seckillStoreDeleteApi(params) {\n  return request({\n    url: '/admin/store/seckill/delete',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 秒杀商品 修改状态\r\n */\nexport function seckillStoreStatusApi(params) {\n  return request({\n    url: '/admin/store/seckill/update/status',\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 秒杀配置 修改状态\r\n */\nexport function seckillConfigStatusApi(id, params) {\n  return request({\n    url: `/admin/store/seckill/manger/update/status/${id}`,\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 砍价商品 列表\r\n */\nexport function bargainListApi(params) {\n  return request({\n    url: `/admin/store/bargain/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 砍价商品 新增\r\n */\nexport function bargainSaveApi(data) {\n  return request({\n    url: `/admin/store/bargain/save`,\n    method: 'POST',\n    data\n  });\n}\n\n/**\r\n * 砍价商品 详情\r\n */\nexport function bargainInfoApi(params) {\n  return request({\n    url: `/admin/store/bargain/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 砍价商品 编辑\r\n */\nexport function bargainUpdateApi(params, data) {\n  return request({\n    url: `/admin/store/bargain/update`,\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 砍价商品 删除\r\n */\nexport function bargainDeleteApi(params) {\n  return request({\n    url: `/admin/store/bargain/delete`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 砍价列表 详情\r\n */\nexport function bargainOrderPinkApi(id) {\n  return request({\n    url: `/admin/store/bargain/bargain_list/${id}`,\n    method: 'get'\n  });\n}\n\n/**\r\n * 砍价列表 列表\r\n */\nexport function bargainListListApi(params) {\n  return request({\n    url: `/admin/store/bargain/bargain_list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团商品 修改拼团状态\r\n */\nexport function bargainStatusApi(params) {\n  return request({\n    url: `/admin/store/bargain/update/status`,\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 拼团商品 列表\r\n */\nexport function combinationListApi(params) {\n  return request({\n    url: `/admin/store/combination/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团商品 删除\r\n */\nexport function combinationDeleteApi(params) {\n  return request({\n    url: `/admin/store/combination/delete`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团商品 新增\r\n */\nexport function combinationSaveApi(data) {\n  return request({\n    url: `/admin/store/combination/save`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 拼团商品 修改\r\n */\nexport function combinationUpdateApi(params, data) {\n  return request({\n    url: `/admin/store/combination/update`,\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 拼团商品 详情\r\n */\nexport function combinationInfoApi(params) {\n  return request({\n    url: `/admin/store/combination/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团商品 修改拼团状态\r\n */\nexport function combinationStatusApi(params) {\n  return request({\n    url: `/admin/store/combination/update/status`,\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 拼团列表 列表\r\n */\nexport function combineListApi(params) {\n  return request({\n    url: `/admin/store/combination/combine/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团列表 统计\r\n */\nexport function combineStatisticsApi(params) {\n  return request({\n    url: `/admin/store/combination/statistics`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团列表 详情\r\n */\nexport function combineOrderPinkApi(id) {\n  return request({\n    url: `/admin/store/combination/order_pink/${id}`,\n    method: 'get'\n  });\n}\n\n/**\r\n * 砍价 导出\r\n */\nexport function exportBargainApi(params) {\n  return request({\n    url: `/admin/export/excel/bargain/product`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 拼团 导出\r\n */\nexport function exportcombiantionApi(params) {\n  return request({\n    url: `/admin/export/excel/combiantion/product`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * @description 活动样式\r\n */\nexport function atuosphereList(params) {\n  return request({\n    url: `/admin/activitystyle/list`,\n    method: 'get',\n    params\n  });\n}\n/**\r\n * @description 活动样式\r\n */\nexport function atmosphereStatusApi(data) {\n  return request({\n    url: `/admin/activitystyle/status`,\n    method: 'post',\n    data\n  });\n}\n/**\r\n * @description 活动样式\r\n */\nexport function atmosphereDelete(params) {\n  return request({\n    url: `/admin/activitystyle/delete`,\n    method: 'get',\n    params\n  });\n}\n/**\r\n * @description 氛围图 -- 选择商品列表 氛围图、活动边框公用同一接口，type传值判断\r\n */\nexport function selectProductList(data) {\n  return request.get(`marketing/spu/lst`, data);\n}\n/**\r\n * @description 氛围图 -- 创建氛围图\r\n */\nexport function createAtuosphere(data) {\n  return request.post(`admin/activitystyle/save`, data);\n}\n/**\r\n * @description 氛围图 -- 编辑氛围图\r\n */\nexport function atuosphereUpdateApi(data) {\n  return request.post(`admin/activitystyle/update`, data);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
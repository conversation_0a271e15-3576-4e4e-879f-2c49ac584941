{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Cookies from 'js-cookie';\n/**\r\n * Created by PanJiaChen on 16/11/18.\r\n */\n/**\r\n * Parse the time to string\r\n * @param {(Object|string|number)} date\r\n * @param {string} fmt\r\n * @returns {string | null}\r\n */\nexport function formatDates(date, fmt) {\n  if (/(y+)/.test(fmt)) {\n    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));\n  }\n  let o = {\n    'M+': date.getMonth() + 1,\n    'd+': date.getDate(),\n    'h+': date.getHours(),\n    'm+': date.getMinutes(),\n    's+': date.getSeconds()\n  };\n  for (let k in o) {\n    if (new RegExp(`(${k})`).test(fmt)) {\n      let str = o[k] + '';\n      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str));\n    }\n  }\n  return fmt;\n}\nfunction padLeftZero(str) {\n  return ('00' + str).substr(str.length);\n}\n\n/**\r\n * 更改时间格式成2010-01-10格式\r\n * @param {(Object|string|number)} time\r\n * @param {string} cFormat\r\n * @returns {string | null}\r\n */\nexport function parseTime(time, cFormat) {\n  if (arguments.length === 0) {\n    return null;\n  }\n  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';\n  let date;\n  if (typeof time === 'object') {\n    date = time;\n  } else {\n    if (typeof time === 'string') {\n      if (/^[0-9]+$/.test(time)) {\n        time = parseInt(time);\n      } else {\n        time = time.replace(new RegExp(/-/gm), '/');\n      }\n    }\n    if (typeof time === 'number' && time.toString().length === 10) {\n      time = time * 1000;\n    }\n    date = new Date(time);\n  }\n  const formatObj = {\n    y: date.getFullYear(),\n    m: date.getMonth() + 1,\n    d: date.getDate(),\n    h: date.getHours(),\n    i: date.getMinutes(),\n    s: date.getSeconds(),\n    a: date.getDay()\n  };\n  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {\n    const value = formatObj[key];\n    // Note: getDay() returns 0 on Sunday\n    if (key === 'a') {\n      return ['日', '一', '二', '三', '四', '五', '六'][value];\n    }\n    return value.toString().padStart(2, '0');\n  });\n  return time_str;\n}\n\n/**\r\n * @param {number} time\r\n * @param {string} option\r\n * @returns {string}\r\n */\nexport function formatTime(time, option) {\n  if (('' + time).length === 10) {\n    time = parseInt(time) * 1000;\n  } else {\n    time = +time;\n  }\n  const d = new Date(time);\n  const now = Date.now();\n  const diff = (now - d) / 1000;\n  if (diff < 30) {\n    return '刚刚';\n  } else if (diff < 3600) {\n    // less 1 hour\n    return Math.ceil(diff / 60) + '分钟前';\n  } else if (diff < 3600 * 24) {\n    return Math.ceil(diff / 3600) + '小时前';\n  } else if (diff < 3600 * 24 * 2) {\n    return '1天前';\n  }\n  if (option) {\n    return parseTime(time, option);\n  } else {\n    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分';\n  }\n}\n\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\nexport function getQueryObject(url) {\n  url = url == null ? window.location.href : url;\n  const search = url.substring(url.lastIndexOf('?') + 1);\n  const obj = {};\n  const reg = /([^?&=]+)=([^?&=]*)/g;\n  search.replace(reg, (rs, $1, $2) => {\n    const name = decodeURIComponent($1);\n    let val = decodeURIComponent($2);\n    val = String(val);\n    obj[name] = val;\n    return rs;\n  });\n  return obj;\n}\n\n/**\r\n * @param {string} input value\r\n * @returns {number} output value\r\n */\nexport function byteLength(str) {\n  // returns the byte length of an utf8 string\n  let s = str.length;\n  for (var i = str.length - 1; i >= 0; i--) {\n    const code = str.charCodeAt(i);\n    if (code > 0x7f && code <= 0x7ff) s++;else if (code > 0x7ff && code <= 0xffff) s += 2;\n    if (code >= 0xdc00 && code <= 0xdfff) i--;\n  }\n  return s;\n}\n\n/**\r\n * @param {Array} actual\r\n * @returns {Array}\r\n */\nexport function cleanArray(actual) {\n  const newArray = [];\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i]) {\n      newArray.push(actual[i]);\n    }\n  }\n  return newArray;\n}\n\n/**\r\n * @param {Object} json\r\n * @returns {Array}\r\n */\nexport function param(json) {\n  if (!json) return '';\n  return cleanArray(Object.keys(json).map(key => {\n    if (json[key] === undefined) return '';\n    return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);\n  })).join('&');\n}\n\n/**\r\n * @param {string} url\r\n * @returns {Object}\r\n */\nexport function param2Obj(url) {\n  const search = url.split('?')[1];\n  if (!search) {\n    return {};\n  }\n  return JSON.parse('{\"' + decodeURIComponent(search).replace(/\"/g, '\\\\\"').replace(/&/g, '\",\"').replace(/=/g, '\":\"').replace(/\\+/g, ' ') + '\"}');\n}\n\n/**\r\n * @param {string} val\r\n * @returns {string}\r\n */\nexport function html2Text(val) {\n  const div = document.createElement('div');\n  div.innerHTML = val;\n  return div.textContent || div.innerText;\n}\n\n/**\r\n * Merges two objects, giving the last one precedence\r\n * @param {Object} target\r\n * @param {(Object|Array)} source\r\n * @returns {Object}\r\n */\nexport function objectMerge(target, source) {\n  if (typeof target !== 'object') {\n    target = {};\n  }\n  if (Array.isArray(source)) {\n    return source.slice();\n  }\n  Object.keys(source).forEach(property => {\n    const sourceProperty = source[property];\n    if (typeof sourceProperty === 'object') {\n      target[property] = objectMerge(target[property], sourceProperty);\n    } else {\n      target[property] = sourceProperty;\n    }\n  });\n  return target;\n}\n\n/**\r\n * @param {HTMLElement} element\r\n * @param {string} className\r\n */\nexport function toggleClass(element, className) {\n  if (!element || !className) {\n    return;\n  }\n  let classString = element.className;\n  const nameIndex = classString.indexOf(className);\n  if (nameIndex === -1) {\n    classString += '' + className;\n  } else {\n    classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length);\n  }\n  element.className = classString;\n}\n\n/**\r\n * @param {string} type\r\n * @returns {Date}\r\n */\nexport function getTime(type) {\n  if (type === 'start') {\n    return new Date().getTime() - 3600 * 1000 * 24 * 90;\n  } else {\n    return new Date(new Date().toDateString());\n  }\n}\n\n/**\r\n * @param {Function} func\r\n * @param {number} wait\r\n * @param {boolean} immediate\r\n * @return {*}\r\n */\nexport function debounce(func, wait, immediate) {\n  let timeout, args, context, timestamp, result;\n  const later = function () {\n    // 据上一次触发时间间隔\n    const last = +new Date() - timestamp;\n\n    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait\n    if (last < wait && last > 0) {\n      timeout = setTimeout(later, wait - last);\n    } else {\n      timeout = null;\n      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用\n      if (!immediate) {\n        result = func.apply(context, args);\n        if (!timeout) context = args = null;\n      }\n    }\n  };\n  return function (...args) {\n    context = this;\n    timestamp = +new Date();\n    const callNow = immediate && !timeout;\n    // 如果延时不存在，重新设定延时\n    if (!timeout) timeout = setTimeout(later, wait);\n    if (callNow) {\n      result = func.apply(context, args);\n      context = args = null;\n    }\n    return result;\n  };\n}\n\n/**\r\n * This is just a simple version of deep copy\r\n * Has a lot of edge cases bug\r\n * If you want to use a perfect deep copy, use lodash's _.cloneDeep\r\n * @param {Object} source\r\n * @returns {Object}\r\n */\nexport function deepClone(source) {\n  if (!source && typeof source !== 'object') {\n    throw new Error('error arguments', 'deepClone');\n  }\n  const targetObj = source.constructor === Array ? [] : {};\n  Object.keys(source).forEach(keys => {\n    if (source[keys] && typeof source[keys] === 'object') {\n      targetObj[keys] = deepClone(source[keys]);\n    } else {\n      targetObj[keys] = source[keys];\n    }\n  });\n  return targetObj;\n}\n\n/**\r\n * @param {Array} arr\r\n * @returns {Array}\r\n */\nexport function uniqueArr(arr) {\n  return Array.from(new Set(arr));\n}\n\n/**\r\n * @returns {string}\r\n */\nexport function createUniqueString() {\n  const timestamp = +new Date() + '';\n  const randomNum = parseInt((1 + Math.random()) * 65536) + '';\n  return (+(randomNum + timestamp)).toString(32);\n}\n\n/**\r\n * Check if an element has a class\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n * @returns {boolean}\r\n */\nexport function hasClass(ele, cls) {\n  return !!ele.className.match(new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)'));\n}\n\n/**\r\n * Add class to element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\nexport function addClass(ele, cls) {\n  if (!hasClass(ele, cls)) ele.className += ' ' + cls;\n}\n\n/**\r\n * Remove class from element\r\n * @param {HTMLElement} elm\r\n * @param {string} cls\r\n */\nexport function removeClass(ele, cls) {\n  if (hasClass(ele, cls)) {\n    const reg = new RegExp('(\\\\s|^)' + cls + '(\\\\s|$)');\n    ele.className = ele.className.replace(reg, ' ');\n  }\n}\n\n/**\r\n * 判断地址\r\n */\nexport function parseQuery() {\n  const res = {};\n  const query = (location.href.split('?')[1] || '').trim().replace(/^(\\?|#|&)/, '');\n  if (!query) {\n    return res;\n  }\n  query.split('&').forEach(param => {\n    const parts = param.replace(/\\+/g, ' ').split('=');\n    const key = decodeURIComponent(parts.shift());\n    const val = parts.length > 0 ? decodeURIComponent(parts.join('=')) : null;\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n  return res;\n}\n\n/**\r\n * 是否是核销员\r\n */\nexport function isWriteOff() {\n  if (localStorage.getItem('storeStaffList')) {\n    let JavaInfo = JSON.parse(Cookies.get('JavaInfo'));\n    let staff = JSON.parse(localStorage.getItem('storeStaffList'));\n    return staff.some(item => item.avatar === JavaInfo.account);\n  }\n}\nexport function getImageDimensions(imageUrl) {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.src = imageUrl;\n    img.onload = () => resolve({\n      width: img.width,\n      height: img.height\n    });\n    img.onerror = () => reject(new Error('无法加载图片'));\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
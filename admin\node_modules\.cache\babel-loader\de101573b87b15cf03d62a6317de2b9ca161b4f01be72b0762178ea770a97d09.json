{"ast": null, "code": "// +---------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +---------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +---------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +---------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +---------------------------------------------------------------------\n\nexport default {\n  shortcuts: [{\n    text: '昨天',\n    onClick: function onClick(picker) {\n      var end = new Date();\n      var start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24);\n      picker.$emit('pick', [start, end]);\n    }\n  }, {\n    text: '最近七天',\n    onClick: function onClick(picker) {\n      var end = new Date();\n      var start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n      picker.$emit('pick', [start, end]);\n    }\n  }, {\n    text: '本月',\n    onClick: function onClick(picker) {\n      var end = new Date();\n      var start = new Date();\n      start.setTime(start.setTime(new Date(new Date().getFullYear(), new Date().getMonth(), 1)));\n      picker.$emit('pick', [start, end]);\n    }\n  }, {\n    text: '最近30天',\n    onClick: function onClick(picker) {\n      var end = new Date();\n      var start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n      picker.$emit('pick', [start, end]);\n    }\n  }, {\n    text: '最近一年',\n    onClick: function onClick(picker) {\n      var end = new Date();\n      var start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);\n      picker.$emit('pick', [start, end]);\n    }\n  }],\n  disabledDate: function disabledDate(time) {\n    var curDate = new Date().getTime();\n    var three = 365 * 24 * 3600 * 1000;\n    var threeMonths = curDate - three;\n    return time.getTime() > Date.now() || time.getTime() < threeMonths;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { saveAs } from 'file-saver';\nimport axios from 'axios';\nimport { getToken } from '@/utils/auth';\nimport { ElMessage } from 'element-plus';\nconst baseURL = process.env.VUE_APP_BASE_API;\nexport default {\n  name(name, isDelete = true) {\n    var url = baseURL + '/common/download?fileName=' + encodeURI(name) + '&delete=' + isDelete;\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(async res => {\n      const isLogin = await this.blobValidate(res.data);\n      if (isLogin) {\n        const blob = new Blob([res.data]);\n        this.saveAs(blob, decodeURI(res.headers['download-filename']));\n      } else {\n        ElMessage.error('无效的会话，或者会话已过期，请重新登录。');\n      }\n    });\n  },\n  resource(resource) {\n    var url = baseURL + '/common/download/resource?resource=' + encodeURI(resource);\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(async res => {\n      const isLogin = await this.blobValidate(res.data);\n      if (isLogin) {\n        const blob = new Blob([res.data]);\n        this.saveAs(blob, decodeURI(res.headers['download-filename']));\n      } else {\n        ElMessage.error('无效的会话，或者会话已过期，请重新登录。');\n      }\n    });\n  },\n  zip(url, name) {\n    var url = baseURL + url;\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(async res => {\n      const isLogin = await this.blobValidate(res.data);\n      if (isLogin) {\n        const blob = new Blob([res.data], {\n          type: 'application/zip'\n        });\n        this.saveAs(blob, name);\n      } else {\n        Message.error('无效的会话，或者会话已过期，请重新登录。');\n      }\n    });\n  },\n  saveAs(text, name, opts) {\n    saveAs(text, name, opts);\n  },\n  async blobValidate(data) {\n    try {\n      const text = await data.text();\n      JSON.parse(text);\n      return false;\n    } catch (error) {\n      return true;\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
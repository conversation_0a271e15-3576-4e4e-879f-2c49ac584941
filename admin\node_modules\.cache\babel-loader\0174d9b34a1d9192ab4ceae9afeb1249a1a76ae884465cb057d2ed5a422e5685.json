{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/** When your routing table is too long, you can split it into small modules **/\n\nimport Layout from '@/layout';\nvar maintainRouter = {\n  path: '/maintain',\n  component: Layout,\n  redirect: '/maintain/devconfiguration/configCategory',\n  name: 'maintain',\n  meta: {\n    title: '维护',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'devconfiguration',\n    name: 'devconfiguration',\n    component: function component() {\n      return import('@/views/maintain');\n    },\n    meta: {\n      title: '开发配置',\n      icon: 'clipboard'\n    },\n    children: [{\n      path: 'configCategory',\n      name: 'configCategory',\n      component: function component() {\n        return import('@/views/maintain/devconfig/configCategroy');\n      },\n      meta: {\n        title: '配置分类',\n        icon: 'clipboard'\n      }\n    }, {\n      path: 'combineddata',\n      name: 'combineddata',\n      component: function component() {\n        return import('@/views/maintain/devconfig/combinedData');\n      },\n      meta: {\n        title: '组合数据',\n        icon: 'clipboard'\n      }\n    }, {\n      path: 'formConfig',\n      name: 'formConfig',\n      component: function component() {\n        return import('@/views/maintain/formConfig/index');\n      },\n      meta: {\n        title: '表单配置',\n        icon: 'clipboard'\n      }\n    }]\n  }, {\n    path: 'user',\n    name: 'MaintainUser',\n    component: function component() {\n      return import('@/views/maintain/user');\n    },\n    meta: {\n      title: '个人中心',\n      icon: 'clipboard'\n    },\n    hidden: true\n  }, {\n    path: 'update',\n    name: 'MaintainUpdate',\n    component: function component() {\n      return import('@/views/maintain/user/update');\n    },\n    meta: {\n      title: '修改密码',\n      icon: 'clipboard'\n    },\n    hidden: true\n  }, {\n    path: 'picture',\n    name: 'picture',\n    component: function component() {\n      return import('@/views/maintain/picture');\n    },\n    meta: {\n      title: '素材管理',\n      icon: 'clipboard'\n    },\n    hidden: false\n  }, {\n    path: 'logistics',\n    name: 'Logistics',\n    alwaysShow: true,\n    redirect: '/logistics/cityList',\n    component: function component() {\n      return import('@/views/maintain');\n    },\n    meta: {\n      title: '物流设置',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'cityList',\n      component: function component() {\n        return import('@/views/maintain/logistics/cityList');\n      },\n      name: 'cityList',\n      meta: {\n        title: '城市数据',\n        icon: ''\n      }\n    }, {\n      path: 'companyList',\n      component: function component() {\n        return import('@/views/maintain/logistics/companyList');\n      },\n      name: 'companyList',\n      meta: {\n        title: '物流公司',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'clearCache',\n    name: 'clearCache',\n    component: function component() {\n      return import('@/views/maintain/clearCache');\n    },\n    meta: {\n      title: '缓存清除',\n      icon: 'clipboard'\n    },\n    hidden: false\n  }, {\n    path: 'schedule',\n    name: 'schedule',\n    component: function component() {\n      return import('@/views/maintain/schedule');\n    },\n    meta: {\n      title: '定时任务管理',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'list',\n      component: function component() {\n        return import('@/views/maintain/schedule/list');\n      },\n      name: 'list',\n      meta: {\n        title: '定时任务',\n        icon: ''\n      }\n    }, {\n      path: 'logList',\n      component: function component() {\n        return import('@/views/maintain/schedule/logList');\n      },\n      name: 'logList',\n      meta: {\n        title: '定时任务日志',\n        icon: ''\n      }\n    }]\n  }]\n};\nexport default maintainRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
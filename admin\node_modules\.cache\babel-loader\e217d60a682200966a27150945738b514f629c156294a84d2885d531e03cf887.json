{"ast": null, "code": "export default {\n  name: 'StoreList',\n  data() {\n    return {\n      list: [{\n        id: 1,\n        storeName: 'iPhone 15 Pro',\n        image: 'https://via.placeholder.com/60x60',\n        price: 7999.00,\n        stock: 100,\n        sales: 50,\n        isShow: 1\n      }, {\n        id: 2,\n        storeName: 'MacBook Pro',\n        image: 'https://via.placeholder.com/60x60',\n        price: 12999.00,\n        stock: 20,\n        sales: 10,\n        isShow: 1\n      }],\n      total: 2,\n      listLoading: false,\n      listQuery: {\n        page: 1,\n        limit: 20,\n        keyword: '',\n        status: ''\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList() {\n      this.listLoading = true;\n      // 这里调用API获取商品列表\n      setTimeout(() => {\n        this.listLoading = false;\n      }, 500);\n    },\n    handleFilter() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleCreate() {\n      this.$message.info('跳转到添加商品页面');\n    },\n    handleEdit(row) {\n      this.$message.info('编辑商品: ' + row.storeName);\n    },\n    handleStatus(row) {\n      const action = row.isShow === 1 ? '下架' : '上架';\n      this.$message.info(action + '商品: ' + row.storeName);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
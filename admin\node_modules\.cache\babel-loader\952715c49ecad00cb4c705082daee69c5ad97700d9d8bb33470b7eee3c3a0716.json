{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"orderdetail\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, _cache[0] || (_cache[0] = [_createElementVNode(\"p\", null, \"OrderDetail\", -1)]));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import auth from './auth';\nimport cache from './cache';\nimport modal from './modal';\nimport download from './download';\nexport default {\n  install: function install(app) {\n    // 认证对象\n    app.config.globalProperties.$auth = auth;\n    // 缓存对象\n    app.config.globalProperties.$cache = cache;\n    // 模态框对象\n    app.config.globalProperties.$modal = modal;\n    // 下载文件\n    app.config.globalProperties.$download = download;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nconst events = [];\nconst $scroll = function (dom, fn) {\n  events.push({\n    dom,\n    fn\n  });\n  fn._index = events.length - 1;\n};\n$scroll.remove = function (fn) {\n  fn._index && events.splice(fn._index, 1);\n};\n\n//上拉加载；\nconst Scroll = {\n  addHandler: function (element, type, handler) {\n    if (element.addEventListener) element.addEventListener(type, handler, false);else if (element.attachEvent) element.attachEvent('on' + type, handler);else element['on' + type] = handler;\n  },\n  listenTouchDirection: function () {\n    this.addHandler(window, 'scroll', function () {\n      const wh = window.innerHeight,\n        st = window.scrollY;\n      events.filter(e => e.dom.scrollHeight && e.dom.scrollHeight > 0).forEach(e => {\n        var dh = e.dom.scrollHeight;\n        var s = Math.ceil(st / (dh - wh) * 100);\n        if (s > 85) e.fn();\n      });\n    });\n  }\n};\nScroll.listenTouchDirection();\nexport default $scroll;\nexport { Scroll };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia';\nimport { login, logout, getInfo } from '@/api/user';\nimport { getToken, setToken, removeToken } from '@/utils/auth';\nimport router, { resetRouter } from '@/router';\nimport { isLoginApi } from '@/api/sms';\nimport Cookies from 'js-cookie';\nimport { ElLoading } from 'element-plus';\nimport * as roleApi from '@/api/roleApi.js';\nimport { formatFlatteningRoutes } from '@/utils/system.js';\nimport * as Auth from '@/libs/wechat';\nexport const useUserStore = defineStore('user', {\n  state: () => ({\n    token: getToken(),\n    name: '',\n    avatar: '',\n    introduction: '',\n    roles: [],\n    isLogin: Cookies.get('isLogin'),\n    permissions: [],\n    captcha: {\n      captchaVerification: '',\n      secretKey: '',\n      token: ''\n    },\n    menuList: JSON.parse(localStorage.getItem('MerPlatAdmin_MenuList')) || [],\n    oneLvMenus: [],\n    oneLvRoutes: JSON.parse(localStorage.getItem('MerPlatAdmin_oneLvRoutes')) || [],\n    childMenuList: []\n  }),\n  getters: {\n    isLoggedIn: state => !!state.token,\n    userRoles: state => state.roles,\n    userPermissions: state => state.permissions\n  },\n  actions: {\n    // 登录\n    async login(userInfo) {\n      const {\n        username,\n        password,\n        captchaVerification\n      } = userInfo;\n      return new Promise((resolve, reject) => {\n        login({\n          username: username.trim(),\n          password: password,\n          captchaVerification\n        }).then(response => {\n          const {\n            data\n          } = response;\n          this.token = data.token;\n          setToken(data.token);\n          resolve();\n        }).catch(error => {\n          reject(error);\n        });\n      });\n    },\n    // 检查登录状态\n    async isLogin(userInfo) {\n      return new Promise((resolve, reject) => {\n        isLoginApi(userInfo).then(response => {\n          resolve(response);\n        }).catch(error => {\n          reject(error);\n        });\n      });\n    },\n    // 获取用户信息\n    async getInfo() {\n      return new Promise((resolve, reject) => {\n        getInfo(this.token).then(response => {\n          const {\n            data\n          } = response;\n          if (!data) {\n            reject('Verification failed, please Login again.');\n          }\n          const {\n            roles,\n            name,\n            avatar,\n            introduction,\n            permissions\n          } = data;\n          if (!roles || roles.length <= 0) {\n            reject('getInfo: roles must be a non-null array!');\n          }\n          this.roles = roles;\n          this.name = name;\n          this.avatar = avatar;\n          this.introduction = introduction;\n          this.permissions = permissions;\n          resolve(data);\n        }).catch(error => {\n          reject(error);\n        });\n      });\n    },\n    // 退出登录\n    async handleLogout() {\n      return new Promise((resolve, reject) => {\n        logout(this.token).then(() => {\n          this.token = '';\n          this.roles = [];\n          this.permissions = [];\n          this.isLogin = false;\n          removeToken();\n          resetRouter();\n\n          // 清除本地存储\n          localStorage.removeItem('MerPlatAdmin_MenuList');\n          localStorage.removeItem('MerPlatAdmin_oneLvRoutes');\n          Cookies.remove('isLogin');\n          resolve();\n        }).catch(error => {\n          reject(error);\n        });\n      });\n    },\n    // 重置token\n    resetToken() {\n      return new Promise(resolve => {\n        this.token = '';\n        this.roles = [];\n        this.permissions = [];\n        removeToken();\n        resolve();\n      });\n    },\n    // 设置token\n    setTokenAction(token) {\n      return new Promise(resolve => {\n        this.token = token;\n        setToken(token);\n        resolve();\n      });\n    },\n    // 获取菜单\n    async getMenus() {\n      const loadingInstance = ElLoading.service({\n        fullscreen: true\n      });\n      return new Promise((resolve, reject) => {\n        roleApi.getMenus().then(res => {\n          let menuList = res.data || [];\n          menuList = this.replaceChildListWithChildren(menuList);\n          this.menuList = menuList;\n          localStorage.setItem('MerPlatAdmin_MenuList', JSON.stringify(menuList));\n          const oneLvRoutes = formatFlatteningRoutes(menuList);\n          this.oneLvRoutes = oneLvRoutes;\n          localStorage.setItem('MerPlatAdmin_oneLvRoutes', JSON.stringify(oneLvRoutes));\n          loadingInstance.close();\n          resolve(res);\n        }).catch(error => {\n          loadingInstance.close();\n          reject(error);\n        });\n      });\n    },\n    // 设置验证码信息\n    setCaptcha(captcha) {\n      this.captcha = captcha;\n    },\n    // 设置登录状态\n    setIsLogin(isLogin) {\n      this.isLogin = isLogin;\n      Cookies.set('isLogin', isLogin);\n    },\n    // 设置菜单列表\n    setMenuList(menuList) {\n      this.menuList = menuList;\n    },\n    // 设置一级菜单\n    setOneLvMenus(oneLvMenus) {\n      this.oneLvMenus = oneLvMenus;\n    },\n    // 设置一级路由\n    setOneLvRoute(oneLvRoutes) {\n      this.oneLvRoutes = oneLvRoutes;\n    },\n    // 设置子菜单列表\n    setChildMenuList(list) {\n      this.childMenuList = list;\n    },\n    // 辅助方法：替换childList为children\n    replaceChildListWithChildren(data) {\n      if (Array.isArray(data)) {\n        return data.map(item => {\n          const newItem = {\n            ...item\n          };\n          if (newItem.childList && Array.isArray(newItem.childList)) {\n            newItem.children = this.replaceChildListWithChildren(newItem.childList);\n            delete newItem.childList;\n          }\n          return newItem;\n        });\n      }\n      return data;\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
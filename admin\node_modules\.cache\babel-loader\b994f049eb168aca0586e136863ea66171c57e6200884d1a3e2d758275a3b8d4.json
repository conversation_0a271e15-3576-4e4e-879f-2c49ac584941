{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default function modalAttr(val, callback, keyNum) {\n  const h = this.$createElement;\n  return new Promise((resolve, reject) => {\n    this.$msgbox({\n      title: '属性规格',\n      customClass: 'upload-form',\n      closeOnClickModal: false,\n      showClose: true,\n      message: h('div', {\n        class: 'common-form-upload'\n      }, [h('attrFrom', {\n        props: {\n          currentRow: val,\n          keyNum: keyNum\n        },\n        on: {\n          getList() {\n            callback();\n          }\n        }\n      })]),\n      showCancelButton: false,\n      showConfirmButton: false\n    }).then(() => {\n      resolve();\n    }).catch(() => {\n      reject();\n      this.$message({\n        type: 'info',\n        message: '已取消'\n      });\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\r\n * v-dialogDragWidth 可拖动弹窗宽度（右侧边）\r\n * Copyright (c) 2019 ruoyi\r\n */\n\nexport default {\n  bind: function bind(el) {\n    var dragDom = el.querySelector('.el-dialog');\n    var lineEl = document.createElement('div');\n    lineEl.style = 'width: 5px; background: inherit; height: 80%; position: absolute; right: 0; top: 0; bottom: 0; margin: auto; z-index: 1; cursor: w-resize;';\n    lineEl.addEventListener('mousedown', function (e) {\n      // 鼠标按下，计算当前元素距离可视区的距离\n      var disX = e.clientX - el.offsetLeft;\n      // 当前宽度\n      var curWidth = dragDom.offsetWidth;\n      document.onmousemove = function (e) {\n        e.preventDefault(); // 移动时禁用默认事件\n        // 通过事件委托，计算移动的距离\n        var l = e.clientX - disX;\n        dragDom.style.width = \"\".concat(curWidth + l, \"px\");\n      };\n      document.onmouseup = function (e) {\n        document.onmousemove = null;\n        document.onmouseup = null;\n      };\n    }, false);\n    dragDom.appendChild(lineEl);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
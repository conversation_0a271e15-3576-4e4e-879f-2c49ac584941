{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\n/**\r\n * 系统内置方法集，正常情况下您不应该修改或移除此文件\r\n * */\n\nimport { cloneDeep } from 'lodash';\n\n/**\r\n * @description 根据当前路由，找打顶部菜单名称\r\n * @param {String} currentPath 当前路径\r\n * @param {Array} menuList 所有路径\r\n * */\nfunction getHeaderName(to, menuList) {\n  const allMenus = [];\n  menuList.forEach(menu => {\n    const headerName = menu.path || '';\n    const menus = transferMenu(menu, headerName);\n    allMenus.push({\n      path: menu.path,\n      header: headerName\n    });\n    menus.forEach(item => allMenus.push(item));\n  });\n  const currentMenu = allMenus.find(item => {\n    let path = to.meta && to.meta.activeMenu ? to.meta.activeMenu : to.path;\n    if (item.path === path) {\n      return true;\n    } else {\n      return path === getPath(to, item.path);\n    }\n  });\n  return currentMenu ? currentMenu.header : null;\n}\nfunction getPath(to, path) {\n  let params = [];\n  let query = [];\n  Object.keys(to.params).forEach(item => {\n    params.push(to.params[item]);\n  });\n  Object.keys(to.query).forEach(item => {\n    query.push(item + '=' + to.query[item]);\n  });\n  return path + (params.length ? '/' + params.join('/') : '') + (query.length ? '?' + query.join('&') : '');\n}\nfunction transferMenu(menu, headerName) {\n  if (menu.children && menu.children.length) {\n    return menu.children.reduce((all, item) => {\n      all.push({\n        path: item.path,\n        header: headerName\n      });\n      const foundChildren = transferMenu(item, headerName);\n      return all.concat(foundChildren);\n    }, []);\n  } else {\n    return [menu];\n  }\n}\nexport { getHeaderName };\n\n/**\r\n * @description 根据当前路由，找打顶部菜单名称\r\n * @param {String} currentPath 当前路径\r\n * @param {Array} menuList 所有路径\r\n * */\nfunction getHeaderSider(menuList) {\n  return menuList.filter(item => item.pid === 0);\n}\nexport { getHeaderSider };\n/**\r\n * @description 根据当前路由，找以及菜单名称\r\n * @param {String} currentPath 当前路径\r\n * @param {Array} menuList 所有路径\r\n * */\nfunction getOneHeaderName(menuList, path) {\n  return menuList.filter(item => item.path === path);\n}\nexport { getOneHeaderName };\n\n/**\r\n * @description 根据当前顶栏菜单 name，找到对应的二级菜单\r\n * @param {Array} menuList 所有的二级菜单\r\n * @param {String} headerName 当前顶栏菜单的 name\r\n * */\nfunction getMenuSider(menuList, headerName = '') {\n  if (headerName) {\n    return menuList.filter(item => item.path === headerName);\n  } else {\n    return menuList;\n  }\n}\nexport { getMenuSider };\n\n/**\r\n * @description 根据当前路由，找到其所有父菜单 path，作为展开侧边栏 open-names 依据\r\n * @param {String} currentPath 当前路径\r\n * @param {Array} menuList 所有路径\r\n * */\n// function getSiderSubmenu (currentPath, menuList) {\n//     const allMenus = [];\n//     menuList.forEach(menu => {\n//         const menus = transferSubMenu(menu, []);\n//         allMenus.push({\n//             path: menu.path,\n//             openNames: []\n//         });\n//         menus.forEach(item => allMenus.push(item));\n//     });\n//     const currentMenu = allMenus.find(item => item.path === currentPath);\n//     return currentMenu ? currentMenu.openNames : [];\n// }\n\nfunction getSiderSubmenu(to, menuList) {\n  const allMenus = [];\n  menuList.forEach(menu => {\n    const menus = transferSubMenu(menu, []);\n    allMenus.push({\n      path: menu.path,\n      openNames: []\n    });\n    menus.forEach(item => allMenus.push(item));\n  });\n  const currentMenu = allMenus.find(item => {\n    if (item.openNames.length) {\n      return item.path === to.path || to.path === getPath(to, item.path);\n    }\n  });\n  return currentMenu ? currentMenu.openNames : [];\n}\nfunction transferSubMenu(menu, openNames) {\n  if (menu.children && menu.children.length) {\n    const itemOpenNames = openNames.concat([menu.path]);\n    return menu.children.reduce((all, item) => {\n      all.push({\n        path: item.path,\n        openNames: itemOpenNames\n      });\n      const foundChildren = transferSubMenu(item, itemOpenNames);\n      return all.concat(foundChildren);\n    }, []);\n  } else {\n    return [menu].map(item => {\n      return {\n        path: item.path,\n        openNames: openNames\n      };\n    });\n  }\n}\nexport { getSiderSubmenu };\n\n/**\r\n * @description 递归获取所有子菜单\r\n * */\nfunction getAllSiderMenu(menuList) {\n  let allMenus = [];\n  menuList.forEach(menu => {\n    if (menu.children && menu.children.length) {\n      const menus = getMenuChildren(menu);\n      menus.forEach(item => allMenus.push(item));\n    } else {\n      allMenus.push(menu);\n    }\n  });\n  return allMenus;\n}\nfunction getMenuChildren(menu) {\n  if (menu.children && menu.children.length) {\n    return menu.children.reduce((all, item) => {\n      const foundChildren = getMenuChildren(item);\n      return all.concat(foundChildren);\n    }, []);\n  } else {\n    return [menu];\n  }\n}\nexport { getAllSiderMenu };\n\n/**\r\n * @description 将菜单转为平级\r\n * */\nfunction flattenSiderMenu(menuList, newList) {\n  menuList.forEach(menu => {\n    let newMenu = {};\n    for (let i in menu) {\n      if (i !== 'children') newMenu[i] = cloneDeep(menu[i]);\n    }\n    newList.push(newMenu);\n    menu.children && flattenSiderMenu(menu.children, newList);\n  });\n  return newList;\n}\nexport { flattenSiderMenu };\nexport const findFirstNonNullChildren = arr => {\n  // 如果数组为空，返回null\n  if (!arr || arr.length === 0) {\n    return null;\n  }\n  // 找到第一个对象\n  const firstObj = arr[0];\n  // 如果第一个对象没有children属性，返回该对象\n  if (!firstObj.children.length) {\n    return firstObj;\n  }\n\n  // 如果第一个对象的children属性是数组，\n  // 递归查找children属性中的第一个非null children属性\n  if (firstObj.children.length && Array.isArray(firstObj.children)) {\n    return findFirstNonNullChildren(firstObj.children);\n  }\n  // 如果数组中没有非null children属性，返回null\n  return null;\n};\nexport const findFirstNonNullChildrenKeys = (obj, lastArr) => {\n  let ids = lastArr;\n  // 如果第一个对象没有children属性，返回该对象\n  if (!obj.children.length) {\n    ids.push(obj.id);\n    return ids;\n  }\n  // 如果第一个对象的children属性是数组，\n  // 递归查找children属性中的第一个非null children属性\n  if (Array.isArray(obj.children) && firstObj.children.length) {\n    ids.push(obj.id);\n    return findFirstNonNullChildrenKeys(obj.children[0], ids);\n  }\n  return ids;\n};\n\n// 多级嵌套数组处理成一维数组\nexport const formatFlatteningRoutes = arr => {\n  if (arr.length <= 0) return false;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i].children && arr[i].children.length) {\n      arr = arr.slice(0, i + 1).concat(arr[i].children, arr.slice(i + 1));\n    }\n  }\n  return arr;\n};\n\n/**\r\n * @description 判断列表1中是否包含了列表2中的某一项\r\n * 因为用户权限 access 为数组，includes 方法无法直接得出结论\r\n * */\nfunction includeArray(list1, list2) {\n  let status = false;\n  if (list1 === true) {\n    return true;\n  } else {\n    if (typeof list2 !== 'object') {\n      return false;\n    }\n    list2.forEach(item => {\n      if (list1.includes(item)) status = true;\n    });\n    return status;\n  }\n}\nexport { includeArray };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
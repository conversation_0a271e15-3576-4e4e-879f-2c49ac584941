{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nvar title = localStorage.getItem('singleAdminSiteName') || 'CRMEB Java';\nexport default function getPageTitle(pageTitle) {\n  if (pageTitle) {\n    return \"\".concat(pageTitle, \" - \").concat(title);\n  }\n  return \"\".concat(title);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
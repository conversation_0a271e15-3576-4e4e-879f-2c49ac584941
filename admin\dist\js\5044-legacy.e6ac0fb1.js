"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[5044],{45044:function(e,t,l){l.r(t),l.d(t,{default:function(){return y}});var o=l(20641),i=l(53751),n=l(90033),a={class:"store-list"},s={class:"filter-container"},r={"slot-scope":"scope"},c={"slot-scope":"scope"},u={"slot-scope":"scope"},d={"slot-scope":"scope"},p={style:{color:"#f56c6c"}},f={"slot-scope":"scope"},h={"slot-scope":"scope"},m={"slot-scope":"scope"},g={"slot-scope":"scope"};function k(e,t,l,k,b,w){var _=(0,o.g2)("el-input"),y=(0,o.g2)("el-option"),v=(0,o.g2)("el-select"),F=(0,o.g2)("el-button"),L=(0,o.g2)("el-table-column"),C=(0,o.g2)("el-image"),S=(0,o.g2)("el-tag"),W=(0,o.g2)("el-table"),Q=(0,o.g2)("pagination"),x=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",a,[(0,o.Lk)("div",s,[(0,o.bF)(_,{modelValue:b.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=function(e){return b.listQuery.keyword=e}),placeholder:"请输入商品名称",style:{width:"200px"},class:"filter-item",onKeyup:(0,i.jR)(w.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),(0,o.bF)(v,{modelValue:b.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=function(e){return b.listQuery.status=e}),placeholder:"商品状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:(0,o.k6)(function(){return[(0,o.bF)(y,{label:"上架",value:"1"}),(0,o.bF)(y,{label:"下架",value:"0"})]}),_:1},8,["modelValue"]),(0,o.bF)(F,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:w.handleFilter},{default:(0,o.k6)(function(){return t[4]||(t[4]=[(0,o.eW)(" 搜索 ",-1)])}),_:1,__:[4]},8,["onClick"]),(0,o.bF)(F,{class:"filter-item",type:"primary",icon:"el-icon-plus",onClick:w.handleCreate},{default:(0,o.k6)(function(){return t[5]||(t[5]=[(0,o.eW)(" 添加商品 ",-1)])}),_:1,__:[5]},8,["onClick"])]),(0,o.bo)(((0,o.uX)(),(0,o.Wv)(W,{data:b.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:(0,o.k6)(function(){return[(0,o.bF)(L,{align:"center",label:"ID",width:"80"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",r,[(0,o.eW)((0,n.v_)(e.scope.row.id),1)])]}),_:1}),(0,o.bF)(L,{label:"商品图片",width:"100"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",c,[(0,o.bF)(C,{style:{width:"60px",height:"60px"},src:e.scope.row.image,fit:"cover"},null,8,["src"])])]}),_:1}),(0,o.bF)(L,{label:"商品名称","min-width":"200"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",u,[(0,o.eW)((0,n.v_)(e.scope.row.storeName),1)])]}),_:1}),(0,o.bF)(L,{label:"价格",width:"100"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",d,[(0,o.Lk)("span",p,"¥"+(0,n.v_)(e.scope.row.price),1)])]}),_:1}),(0,o.bF)(L,{label:"库存",width:"80"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",f,[(0,o.eW)((0,n.v_)(e.scope.row.stock),1)])]}),_:1}),(0,o.bF)(L,{label:"销量",width:"80"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",h,[(0,o.eW)((0,n.v_)(e.scope.row.sales),1)])]}),_:1}),(0,o.bF)(L,{label:"状态",width:"100"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",m,[(0,o.bF)(S,{type:1===e.scope.row.isShow?"success":"danger"},{default:(0,o.k6)(function(){return[(0,o.eW)((0,n.v_)(1===e.scope.row.isShow?"上架":"下架"),1)]}),_:1},8,["type"])])]}),_:1}),(0,o.bF)(L,{label:"操作",align:"center",width:"200"},{default:(0,o.k6)(function(){return[(0,o.Lk)("template",g,[(0,o.bF)(F,{size:"mini",type:"primary",onClick:t[2]||(t[2]=function(t){return w.handleEdit(e.scope.row)})},{default:(0,o.k6)(function(){return t[6]||(t[6]=[(0,o.eW)(" 编辑 ",-1)])}),_:1,__:[6]}),(0,o.bF)(F,{size:"mini",type:1===e.scope.row.isShow?"danger":"success",onClick:t[3]||(t[3]=function(t){return w.handleStatus(e.scope.row)})},{default:(0,o.k6)(function(){return[(0,o.eW)((0,n.v_)(1===e.scope.row.isShow?"下架":"上架"),1)]}),_:1},8,["type"])])]}),_:1})]}),_:1},8,["data"])),[[x,b.listLoading]]),(0,o.bo)((0,o.bF)(Q,{total:b.total,page:b.listQuery.page,limit:b.listQuery.limit,onPagination:w.getList},null,8,["total","page","limit","onPagination"]),[[i.aG,b.total>0]])])}var b={name:"StoreList",data:function(){return{list:[{id:1,storeName:"iPhone 15 Pro",image:"https://via.placeholder.com/60x60",price:7999,stock:100,sales:50,isShow:1},{id:2,storeName:"MacBook Pro",image:"https://via.placeholder.com/60x60",price:12999,stock:20,sales:10,isShow:1}],total:2,listLoading:!1,listQuery:{page:1,limit:20,keyword:"",status:""}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.listLoading=!0,setTimeout(function(){e.listLoading=!1},500)},handleFilter:function(){this.listQuery.page=1,this.getList()},handleCreate:function(){this.$message.info("跳转到添加商品页面")},handleEdit:function(e){this.$message.info("编辑商品: "+e.storeName)},handleStatus:function(e){var t=1===e.isShow?"下架":"上架";this.$message.info(t+"商品: "+e.storeName)}}},w=l(66262);const _=(0,w.A)(b,[["render",k],["__scopeId","data-v-bc1ac09c"]]);var y=_}}]);
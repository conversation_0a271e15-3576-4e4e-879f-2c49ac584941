{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"error-page\"\n};\nconst _hoisted_2 = {\n  class: \"error-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"h1\", null, \"401\", -1)), _cache[2] || (_cache[2] = _createElementVNode(\"h2\", null, \"未授权访问\", -1)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", null, \"抱歉，您没有权限访问此页面\", -1)), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $options.goBack\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"返回\", -1)])),\n    _: 1,\n    __: [0]\n  }, 8, [\"onClick\"])])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
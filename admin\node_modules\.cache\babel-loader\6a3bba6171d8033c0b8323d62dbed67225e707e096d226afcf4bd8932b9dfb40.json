{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n//订单过滤器\n\n/**\r\n * @description 支付状态\r\n */\nexport function paidFilter(status) {\n  const statusMap = {\n    true: '已支付',\n    false: '未支付'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 订单状态\r\n * 2,已收货，待评价\r\n */\nexport function orderStatusFilter(status) {\n  const statusMap = {\n    0: '待发货',\n    1: '待收货',\n    2: '已收货',\n    3: '待评价',\n    '-2': '已退款',\n    '-1': '退款中'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 退款状态\r\n * 2,已收货，待评价\r\n */\nexport function refundStatusFilter(status) {\n  const statusMap = {\n    0: '未退款',\n    1: '申请中',\n    2: '已退款',\n    3: '退款中'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 支付方式\r\n */\nexport function payTypeFilter(status) {\n  const statusMap = {\n    weixin: '微信',\n    alipay: '支付宝',\n    yue: '余额'\n  };\n  return statusMap[status] || '-';\n}\n\n/**\r\n * @description 订单类型\r\n */\nexport function orderTypeFilter(status) {\n  const statusMap = {\n    1: '普通订单',\n    2: '核销订单'\n  };\n  return statusMap[status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
#!/bin/bash

echo "========================================"
echo "CRMEB数据库修复脚本"
echo "========================================"

# 数据库配置
DB_HOST="localhost"
DB_NAME="crmeb"
DB_USER="crmeb"
DB_PASS="@Ckr015410"

echo "第一步：检查数据库连接..."
mysql -u $DB_USER -p$DB_PASS -h $DB_HOST -e "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败，请检查配置"
    exit 1
fi

echo ""
echo "第二步：检查数据库表..."
TABLE_COUNT=$(mysql -u $DB_USER -p$DB_PASS -h $DB_HOST -e "USE $DB_NAME; SHOW TABLES;" 2>/dev/null | wc -l)
echo "当前数据库表数量: $TABLE_COUNT"

if [ $TABLE_COUNT -lt 10 ]; then
    echo ""
    echo "第三步：下载并导入数据库结构..."
    cd /www/wwwroot/crmeb/
    
    # 尝试从多个源下载数据库文件
    if [ ! -f "crmeb.sql" ]; then
        echo "正在下载数据库文件..."
        wget -O crmeb.sql https://gitee.com/ZhongBangKeJi/crmeb_java/raw/master/sql/crmeb.sql || \
        wget -O crmeb.sql https://raw.githubusercontent.com/crmeb/crmeb_java/master/sql/crmeb.sql || \
        curl -o crmeb.sql https://gitee.com/ZhongBangKeJi/crmeb_java/raw/master/sql/crmeb.sql
        
        if [ ! -f "crmeb.sql" ]; then
            echo "❌ 无法下载数据库文件，请手动下载 crmeb.sql 文件"
            echo "下载地址: https://gitee.com/ZhongBangKeJi/crmeb_java/blob/master/sql/crmeb.sql"
            exit 1
        fi
    fi
    
    echo "正在导入数据库..."
    mysql -u $DB_USER -p$DB_PASS -h $DB_HOST $DB_NAME < crmeb.sql
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据库导入成功"
    else
        echo "❌ 数据库导入失败"
        exit 1
    fi
else
    echo "✅ 数据库表已存在，跳过导入"
fi

echo ""
echo "第四步：验证数据库表..."
NEW_TABLE_COUNT=$(mysql -u $DB_USER -p$DB_PASS -h $DB_HOST -e "USE $DB_NAME; SHOW TABLES;" | wc -l)
echo "导入后数据库表数量: $NEW_TABLE_COUNT"

echo ""
echo "第五步：重启应用..."
cd /www/wwwroot/crmeb/
pkill -f "Crmeb-front.jar" || true
sleep 2

nohup java -jar Crmeb-front.jar --spring.profiles.active=prod > crmeb.log 2>&1 &
echo "应用已启动，PID: $!"

echo ""
echo "第六步：检查启动状态..."
sleep 5
if ps aux | grep -q "[C]rmeb-front.jar"; then
    echo "✅ 应用启动成功"
    echo "查看日志: tail -f /www/wwwroot/crmeb/crmeb.log"
    echo "测试API: curl http://localhost:8081/api/front/index"
else
    echo "❌ 应用启动失败，请查看日志"
    echo "查看日志: tail -f /www/wwwroot/crmeb/crmeb.log"
fi

echo ""
echo "========================================"
echo "修复脚本执行完成！"
echo "========================================"

{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport axios from 'axios';\nimport store from '@/store';\nimport { ElMessage } from 'element-plus';\nvar SettingMer = require('@/settings');\n\n// 小程序版本的请求工具\nvar service = axios.create({\n  baseURL: SettingMer.apiBaseURL,\n  timeout: 60000 // 过期时间\n});\n\n// request interceptor\nservice.interceptors.request.use(function (config) {\n  // 发送请求之前做的\n  var token = !store.getters.token ? sessionStorage.getItem('token') : store.getters.token;\n  if (token) {\n    config.headers['Authori-zation'] = token;\n  }\n  if (/get/i.test(config.method)) {\n    config.params = config.params || {};\n    config.params.temp = Date.parse(new Date()) / 1000;\n  }\n  return config;\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// response interceptor\nservice.interceptors.response.use(function (response) {\n  var res = response.data;\n  // if the custom code is not 20000, it is judged as an error.\n  if (res.code === 401) {\n    // to re-login\n    ElMessage.error('无效的会话，或者登录已过期，请重新登录。');\n    if (window.location.pathname !== '/login') location.href = '/login';\n  } else if (res.code === 403) {\n    ElMessage.error('没有权限访问。');\n  }\n  if (res.code !== 200 && res.code !== 401) {\n    if (isPhone()) {\n      //移动端\n      return Promise.reject(res || 'Error');\n    }\n    ElMessage({\n      message: res.message || 'Error',\n      type: 'error',\n      duration: 5 * 1000\n    });\n    return Promise.reject();\n  } else {\n    return res.data;\n  }\n}, function (error) {\n  ElMessage({\n    message: error.message,\n    type: 'error',\n    duration: 5 * 1000\n  });\n  return Promise.reject(error);\n});\nexport default service;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export default {\n  name: 'Auth-redirect',\n  props: {\n    // 组件属性\n    value: {\n      type: [String, Number, Boolean, Array, Object],\n      default: null\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      // 组件数据\n    };\n  },\n  computed: {\n    // 计算属性\n  },\n  methods: {\n    // 组件方法\n    handleChange(value) {\n      this.$emit('input', value);\n      this.$emit('change', value);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[8580],{8580:function(e,t,a){a.r(t),a.d(t,{default:function(){return _}});var l=a(641),s=a(3751),i=a(33);const o={class:"user-list"},n={class:"filter-container"},r={"slot-scope":"scope"},c={"slot-scope":"scope"},u={"slot-scope":"scope"},d={"slot-scope":"scope"},p={"slot-scope":"scope"},m={"slot-scope":"scope"};function g(e,t,a,g,h,k){const b=(0,l.g2)("el-input"),_=(0,l.g2)("el-button"),f=(0,l.g2)("el-table-column"),w=(0,l.g2)("el-tag"),y=(0,l.g2)("el-table"),L=(0,l.g2)("pagination"),F=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.Lk)("div",n,[(0,l.bF)(b,{modelValue:h.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>h.listQuery.keyword=e),placeholder:"请输入用户名或手机号",style:{width:"200px"},class:"filter-item",onKeyup:(0,s.jR)(k.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),(0,l.bF)(_,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:k.handleFilter},{default:(0,l.k6)(()=>t[3]||(t[3]=[(0,l.eW)(" 搜索 ",-1)])),_:1,__:[3]},8,["onClick"])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(y,{data:h.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:(0,l.k6)(()=>[(0,l.bF)(f,{align:"center",label:"ID",width:"95"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",r,[(0,l.eW)((0,i.v_)(e.scope.row.id),1)])]),_:1}),(0,l.bF)(f,{label:"用户名"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",c,[(0,l.eW)((0,i.v_)(e.scope.row.username),1)])]),_:1}),(0,l.bF)(f,{label:"手机号",width:"120"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",u,[(0,l.Lk)("span",null,(0,i.v_)(e.scope.row.phone),1)])]),_:1}),(0,l.bF)(f,{label:"注册时间",width:"150"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",d,[t[4]||(t[4]=(0,l.Lk)("i",{class:"el-icon-time"},null,-1)),(0,l.Lk)("span",null,(0,i.v_)(e.scope.row.createTime),1)])]),_:1}),(0,l.bF)(f,{label:"状态",width:"100"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",p,[(0,l.bF)(w,{type:1===e.scope.row.status?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,i.v_)(1===e.scope.row.status?"正常":"禁用"),1)]),_:1},8,["type"])])]),_:1}),(0,l.bF)(f,{label:"操作",align:"center",width:"200"},{default:(0,l.k6)(()=>[(0,l.Lk)("template",m,[(0,l.bF)(_,{size:"mini",type:"primary",onClick:t[1]||(t[1]=t=>k.handleEdit(e.scope.row))},{default:(0,l.k6)(()=>t[5]||(t[5]=[(0,l.eW)(" 编辑 ",-1)])),_:1,__:[5]}),(0,l.bF)(_,{size:"mini",type:1===e.scope.row.status?"danger":"success",onClick:t[2]||(t[2]=t=>k.handleStatus(e.scope.row))},{default:(0,l.k6)(()=>[(0,l.eW)((0,i.v_)(1===e.scope.row.status?"禁用":"启用"),1)]),_:1},8,["type"])])]),_:1})]),_:1},8,["data"])),[[F,h.listLoading]]),(0,l.bo)((0,l.bF)(L,{total:h.total,page:h.listQuery.page,limit:h.listQuery.limit,onPagination:k.getList},null,8,["total","page","limit","onPagination"]),[[s.aG,h.total>0]])])}var h={name:"UserList",data(){return{list:[{id:1,username:"admin",phone:"13800138000",createTime:"2025-01-01 10:00:00",status:1},{id:2,username:"user001",phone:"13800138001",createTime:"2025-01-02 11:00:00",status:1}],total:2,listLoading:!1,listQuery:{page:1,limit:20,keyword:""}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,setTimeout(()=>{this.listLoading=!1},500)},handleFilter(){this.listQuery.page=1,this.getList()},handleEdit(e){this.$message.info("编辑用户: "+e.username)},handleStatus(e){const t=1===e.status?"禁用":"启用";this.$message.info(t+"用户: "+e.username)}}},k=a(6262);const b=(0,k.A)(h,[["render",g],["__scopeId","data-v-7b6e4a0c"]]);var _=b}}]);
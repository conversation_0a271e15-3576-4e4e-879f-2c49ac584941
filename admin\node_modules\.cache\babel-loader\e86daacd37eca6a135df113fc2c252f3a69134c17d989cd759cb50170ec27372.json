{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n//小程序 微信过滤器\nimport Cookies from 'js-cookie';\n/**\r\n * @description 小程序所属类目\r\n */\nexport function wxCategoryFilter(status) {\n  if (!status) {\n    return '';\n  }\n  if (!Cookies.get('WxCategory')) {\n    return;\n  }\n  let arrayList = JSON.parse(Cookies.get('WxCategory'));\n  if (arrayList.filter(item => Number(status) === Number(item.id)).length < 1) {\n    return '';\n  }\n  return arrayList.filter(item => Number(status) === Number(item.id))[0].name;\n}\n\n/**\r\n * @description 小程序模板类型\r\n */\nexport function wxTypeFilter(status) {\n  const statusMap = {\n    2: '一次性订阅',\n    3: '长期订阅'\n  };\n  return statusMap[status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.utils.CrmebDateUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.UserLoginSecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户登录安全服务实现类
 * 提供登录失败锁定、设备管理、异常登录检测等安全功能
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class UserLoginSecurityServiceImpl implements UserLoginSecurityService {

    @Autowired
    private RedisUtil redisUtil;

    // Redis Key前缀
    private static final String LOGIN_FAIL_COUNT_KEY = "user:login:fail:count:";
    private static final String LOGIN_LOCK_KEY = "user:login:lock:";
    private static final String USER_DEVICE_KEY = "user:device:";
    private static final String LOGIN_HISTORY_KEY = "user:login:history:";
    private static final String SUSPICIOUS_LOGIN_KEY = "user:suspicious:login:";
    private static final String IP_LOGIN_COUNT_KEY = "ip:login:count:";

    // 安全配置常量
    private static final int MAX_LOGIN_FAIL_COUNT = 5;          // 最大登录失败次数
    private static final int LOGIN_LOCK_TIME = 30 * 60;        // 锁定时间（秒）
    private static final int MAX_DEVICES_PER_USER = 3;         // 每个用户最大设备数
    private static final int LOGIN_HISTORY_DAYS = 30;          // 登录历史保存天数
    private static final int MAX_IP_LOGIN_PER_HOUR = 20;       // 每小时每IP最大登录次数
    private static final int SUSPICIOUS_LOGIN_THRESHOLD = 10;   // 可疑登录阈值

    @Override
    public Boolean checkLoginFailCount(String account) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        String key = LOGIN_FAIL_COUNT_KEY + account;
        Integer failCount = (Integer) redisUtil.get(key);
        return ObjectUtil.isNull(failCount) || failCount < MAX_LOGIN_FAIL_COUNT;
    }

    @Override
    public void recordLoginFail(String account, String ip, String userAgent) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        String countKey = LOGIN_FAIL_COUNT_KEY + account;
        Integer failCount = (Integer) redisUtil.get(countKey);
        failCount = ObjectUtil.isNull(failCount) ? 1 : failCount + 1;

        // 设置失败次数，过期时间1小时
        redisUtil.set(countKey, failCount, 3600L);

        // 如果达到最大失败次数，锁定账户
        if (failCount >= MAX_LOGIN_FAIL_COUNT) {
            lockAccount(account);
            log.warn("账户{}登录失败次数达到上限，已锁定{}秒", account, LOGIN_LOCK_TIME);
        }

        // 记录可疑登录
        recordSuspiciousLogin(account, ip, userAgent, "登录失败次数过多");

        log.info("记录登录失败：账户={}, IP={}, 失败次数={}", account, ip, failCount);
    }

    @Override
    public void clearLoginFailCount(String account) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        String key = LOGIN_FAIL_COUNT_KEY + account;
        redisUtil.delete(key);
    }

    @Override
    public Boolean isAccountLocked(String account) {
        if (StrUtil.isBlank(account)) {
            return false;
        }

        String key = LOGIN_LOCK_KEY + account;
        return redisUtil.exists(key);
    }

    @Override
    public void lockAccount(String account) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        String key = LOGIN_LOCK_KEY + account;
        redisUtil.set(key, CrmebDateUtil.nowDateTime(), (long) LOGIN_LOCK_TIME);
    }

    @Override
    public void unlockAccount(String account) {
        if (StrUtil.isBlank(account)) {
            return;
        }

        String lockKey = LOGIN_LOCK_KEY + account;
        String countKey = LOGIN_FAIL_COUNT_KEY + account;
        redisUtil.delete(lockKey);
        redisUtil.delete(countKey);
    }

    @Override
    public Long getAccountLockRemainingTime(String account) {
        if (StrUtil.isBlank(account)) {
            return 0L;
        }

        String key = LOGIN_LOCK_KEY + account;
        return redisUtil.getExpire(key);
    }

    @Override
    public Boolean checkDeviceLimit(Integer userId, String deviceId) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(deviceId)) {
            return false;
        }

        String key = USER_DEVICE_KEY + userId;
        Set<String> devices = (Set<String>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(devices)) {
            devices = new HashSet<>();
        }

        // 如果设备已存在，直接通过
        if (devices.contains(deviceId)) {
            return true;
        }

        // 检查设备数量限制
        return devices.size() < MAX_DEVICES_PER_USER;
    }

    @Override
    public void addUserDevice(Integer userId, String deviceId, String deviceInfo) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(deviceId)) {
            return;
        }

        String key = USER_DEVICE_KEY + userId;
        Set<String> devices = (Set<String>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(devices)) {
            devices = new HashSet<>();
        }

        devices.add(deviceId);
        
        // 如果超过限制，移除最旧的设备
        if (devices.size() > MAX_DEVICES_PER_USER) {
            Iterator<String> iterator = devices.iterator();
            iterator.next();
            iterator.remove();
        }

        // 设置过期时间30天
        redisUtil.set(key, devices, 30L * 24 * 3600);

        log.info("添加用户设备：userId={}, deviceId={}, deviceInfo={}", userId, deviceId, deviceInfo);
    }

    @Override
    public void removeUserDevice(Integer userId, String deviceId) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(deviceId)) {
            return;
        }

        String key = USER_DEVICE_KEY + userId;
        Set<String> devices = (Set<String>) redisUtil.get(key);
        
        if (ObjectUtil.isNotNull(devices)) {
            devices.remove(deviceId);
            redisUtil.set(key, devices, 30L * 24 * 3600);
        }

        log.info("移除用户设备：userId={}, deviceId={}", userId, deviceId);
    }

    @Override
    public List<String> getUserDevices(Integer userId) {
        if (ObjectUtil.isNull(userId)) {
            return new ArrayList<>();
        }

        String key = USER_DEVICE_KEY + userId;
        Set<String> devices = (Set<String>) redisUtil.get(key);
        
        return ObjectUtil.isNull(devices) ? new ArrayList<>() : new ArrayList<>(devices);
    }

    @Override
    public void recordLoginHistory(Integer userId, String ip, String userAgent, String deviceId, Boolean success) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }

        String key = LOGIN_HISTORY_KEY + userId;
        List<Map<String, Object>> history = (List<Map<String, Object>>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(history)) {
            history = new ArrayList<>();
        }

        Map<String, Object> record = new HashMap<>();
        record.put("ip", ip);
        record.put("userAgent", userAgent);
        record.put("deviceId", deviceId);
        record.put("success", success);
        record.put("loginTime", CrmebDateUtil.nowDateTime());

        history.add(0, record); // 添加到列表开头

        // 保持最近100条记录
        if (history.size() > 100) {
            history = history.subList(0, 100);
        }

        // 设置过期时间30天
        redisUtil.set(key, history, LOGIN_HISTORY_DAYS * 24L * 3600);
    }

    @Override
    public List<Map<String, Object>> getLoginHistory(Integer userId, Integer limit) {
        if (ObjectUtil.isNull(userId)) {
            return new ArrayList<>();
        }

        String key = LOGIN_HISTORY_KEY + userId;
        List<Map<String, Object>> history = (List<Map<String, Object>>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(history)) {
            return new ArrayList<>();
        }

        if (ObjectUtil.isNotNull(limit) && limit > 0 && history.size() > limit) {
            return history.subList(0, limit);
        }

        return history;
    }

    @Override
    public Boolean checkIpLoginLimit(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }

        String key = IP_LOGIN_COUNT_KEY + ip + ":" + CrmebDateUtil.nowDateTimeStr().substring(0, 13); // 精确到小时
        Integer count = (Integer) redisUtil.get(key);
        
        return ObjectUtil.isNull(count) || count < MAX_IP_LOGIN_PER_HOUR;
    }

    @Override
    public void recordIpLogin(String ip) {
        if (StrUtil.isBlank(ip)) {
            return;
        }

        String key = IP_LOGIN_COUNT_KEY + ip + ":" + CrmebDateUtil.nowDateTimeStr().substring(0, 13); // 精确到小时
        Integer count = (Integer) redisUtil.get(key);
        count = ObjectUtil.isNull(count) ? 1 : count + 1;
        
        // 设置过期时间1小时
        redisUtil.set(key, count, 3600L);
    }

    @Override
    public Boolean detectSuspiciousLogin(Integer userId, String ip, String userAgent) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(ip)) {
            return false;
        }

        // 获取用户最近的登录历史
        List<Map<String, Object>> history = getLoginHistory(userId, 10);
        
        if (CollUtil.isEmpty(history)) {
            return false;
        }

        // 检查IP变化频率
        Set<String> recentIps = new HashSet<>();
        for (Map<String, Object> record : history) {
            recentIps.add((String) record.get("ip"));
        }

        // 如果最近10次登录使用了超过5个不同IP，标记为可疑
        if (recentIps.size() > 5) {
            recordSuspiciousLogin(userId.toString(), ip, userAgent, "IP变化频繁");
            return true;
        }

        // 检查是否为新IP
        boolean isNewIp = recentIps.stream().noneMatch(recentIp -> recentIp.equals(ip));
        if (isNewIp && recentIps.size() >= 3) {
            recordSuspiciousLogin(userId.toString(), ip, userAgent, "使用新IP登录");
            return true;
        }

        return false;
    }

    @Override
    public void recordSuspiciousLogin(String account, String ip, String userAgent, String reason) {
        if (StrUtil.isBlank(account) || StrUtil.isBlank(ip)) {
            return;
        }

        String key = SUSPICIOUS_LOGIN_KEY + account;
        List<Map<String, Object>> suspiciousRecords = (List<Map<String, Object>>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(suspiciousRecords)) {
            suspiciousRecords = new ArrayList<>();
        }

        Map<String, Object> record = new HashMap<>();
        record.put("ip", ip);
        record.put("userAgent", userAgent);
        record.put("reason", reason);
        record.put("time", CrmebDateUtil.nowDateTime());

        suspiciousRecords.add(0, record);

        // 保持最近50条记录
        if (suspiciousRecords.size() > 50) {
            suspiciousRecords = suspiciousRecords.subList(0, 50);
        }

        // 设置过期时间7天
        redisUtil.set(key, suspiciousRecords, 7L * 24 * 3600);

        log.warn("记录可疑登录：账户={}, IP={}, 原因={}", account, ip, reason);
    }

    @Override
    public List<Map<String, Object>> getSuspiciousLoginRecords(String account, Integer limit) {
        if (StrUtil.isBlank(account)) {
            return new ArrayList<>();
        }

        String key = SUSPICIOUS_LOGIN_KEY + account;
        List<Map<String, Object>> records = (List<Map<String, Object>>) redisUtil.get(key);
        
        if (ObjectUtil.isNull(records)) {
            return new ArrayList<>();
        }

        if (ObjectUtil.isNotNull(limit) && limit > 0 && records.size() > limit) {
            return records.subList(0, limit);
        }

        return records;
    }

    @Override
    public Map<String, Object> getSecurityStatus(Integer userId, String account) {
        Map<String, Object> status = new HashMap<>();
        
        // 账户锁定状态
        status.put("isLocked", isAccountLocked(account));
        status.put("lockRemainingTime", getAccountLockRemainingTime(account));
        
        // 登录失败次数
        String countKey = LOGIN_FAIL_COUNT_KEY + account;
        Integer failCount = (Integer) redisUtil.get(countKey);
        status.put("loginFailCount", ObjectUtil.isNull(failCount) ? 0 : failCount);
        
        // 设备信息
        List<String> devices = getUserDevices(userId);
        status.put("deviceCount", devices.size());
        status.put("devices", devices);
        
        // 可疑登录记录
        List<Map<String, Object>> suspiciousRecords = getSuspiciousLoginRecords(account, 5);
        status.put("suspiciousLoginCount", suspiciousRecords.size());
        status.put("recentSuspiciousLogins", suspiciousRecords);
        
        // 最近登录历史
        List<Map<String, Object>> loginHistory = getLoginHistory(userId, 5);
        status.put("recentLogins", loginHistory);
        
        return status;
    }
}
{"ast": null, "code": "import \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport Vue from 'vue';\nimport SvgIcon from '@/components/SvgIcon'; // svg component\n\n// register globally\nVue.component('svg-icon', SvgIcon);\nvar req = require.context('./svg', false, /\\.svg$/);\nvar requireAll = function requireAll(requireContext) {\n  return requireContext.keys().map(requireContext);\n};\nrequireAll(req);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
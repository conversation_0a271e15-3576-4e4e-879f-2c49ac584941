{"ast": null, "code": "import _createForOfIteratorHelper from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js\";\nimport _typeof from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.constructor.js\";\nimport \"core-js/modules/es.regexp.dot-all.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.sticky.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/**\r\n * 通用js方法封装处理\r\n * Copyright (c) 2019 ruoyi\r\n */\n\n// 日期格式化\nexport function parseTime(time, pattern) {\n  if (arguments.length === 0 || !time) {\n    return null;\n  }\n  var format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';\n  var date;\n  if (_typeof(time) === 'object') {\n    date = time;\n  } else {\n    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {\n      time = parseInt(time);\n    } else if (typeof time === 'string') {\n      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\\.[\\d]{3}/gm), '');\n    }\n    if (typeof time === 'number' && time.toString().length === 10) {\n      time = time * 1000;\n    }\n    date = new Date(time);\n  }\n  var formatObj = {\n    y: date.getFullYear(),\n    m: date.getMonth() + 1,\n    d: date.getDate(),\n    h: date.getHours(),\n    i: date.getMinutes(),\n    s: date.getSeconds(),\n    a: date.getDay()\n  };\n  var time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, function (result, key) {\n    var value = formatObj[key];\n    // Note: getDay() returns 0 on Sunday\n    if (key === 'a') {\n      return ['日', '一', '二', '三', '四', '五', '六'][value];\n    }\n    if (result.length > 0 && value < 10) {\n      value = '0' + value;\n    }\n    return value || 0;\n  });\n  return time_str;\n}\n\n// 表单重置\nexport function resetForm(refName) {\n  if (this.$refs[refName]) {\n    this.$refs[refName].resetFields();\n  }\n}\n\n// 添加日期范围\nexport function addDateRange(params, dateRange, propName) {\n  var search = params;\n  search.params = _typeof(search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};\n  dateRange = Array.isArray(dateRange) ? dateRange : [];\n  if (typeof propName === 'undefined') {\n    search.params['beginTime'] = dateRange[0];\n    search.params['endTime'] = dateRange[1];\n  } else {\n    search.params['begin' + propName] = dateRange[0];\n    search.params['end' + propName] = dateRange[1];\n  }\n  return search;\n}\n\n// 字符串格式化(%s )\nexport function sprintf(str) {\n  var args = arguments,\n    flag = true,\n    i = 1;\n  str = str.replace(/%s/g, function () {\n    var arg = args[i++];\n    if (typeof arg === 'undefined') {\n      flag = false;\n      return '';\n    }\n    return arg;\n  });\n  return flag ? str : '';\n}\n\n// 转换字符串，undefined,null等转化为\"\"\nexport function praseStrEmpty(str) {\n  if (!str || str == 'undefined' || str == 'null') {\n    return '';\n  }\n  return str;\n}\n\n// 数据合并\nexport function mergeRecursive(source, target) {\n  for (var p in target) {\n    try {\n      if (target[p].constructor == Object) {\n        source[p] = mergeRecursive(source[p], target[p]);\n      } else {\n        source[p] = target[p];\n      }\n    } catch (e) {\n      source[p] = target[p];\n    }\n  }\n  return source;\n}\n\n/**\r\n * 构造树型结构数据\r\n * @param {*} data 数据源\r\n * @param {*} id id字段 默认 'id'\r\n * @param {*} parentId 父节点字段 默认 'parentId'\r\n * @param {*} children 孩子节点字段 默认 'children'\r\n */\nexport function handleTree(data, id, parentId, children) {\n  var config = {\n    id: id || 'id',\n    parentId: parentId || 'parentId',\n    childrenList: children || 'children'\n  };\n  var childrenListMap = {};\n  var nodeIds = {};\n  var tree = [];\n  var _iterator = _createForOfIteratorHelper(data),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var d = _step.value;\n      var _parentId = d[config.parentId];\n      if (childrenListMap[_parentId] == null) {\n        childrenListMap[_parentId] = [];\n      }\n      nodeIds[d.id] = d;\n      childrenListMap[_parentId].push(d);\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  var _iterator2 = _createForOfIteratorHelper(data),\n    _step2;\n  try {\n    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n      var _d = _step2.value;\n      var _parentId2 = _d[config.parentId];\n      if (nodeIds[_parentId2] == null) {\n        tree.push(_d);\n      }\n    }\n  } catch (err) {\n    _iterator2.e(err);\n  } finally {\n    _iterator2.f();\n  }\n  for (var _i = 0, _tree = tree; _i < _tree.length; _i++) {\n    var t = _tree[_i];\n    adaptToChildrenList(t);\n  }\n  function adaptToChildrenList(o) {\n    if (childrenListMap[o.id] !== null) {\n      o[config.childrenList] = childrenListMap[o.id];\n    }\n    if (o[config.childrenList]) {\n      var _iterator3 = _createForOfIteratorHelper(o[config.childrenList]),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var c = _step3.value;\n          adaptToChildrenList(c);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n    }\n  }\n  return tree;\n}\n\n/**\r\n * 路由数据遍历\r\n *\r\n */\nexport function formatRoutes(routerArr) {\n  var arr = [],\n    obj = {};\n  routerArr.forEach(function (tmp) {\n    obj = {\n      id: tmp.id,\n      pid: tmp.pid,\n      name: tmp.name,\n      url: tmp.component,\n      path: '/' + tmp.pid + '/',\n      perms: tmp.perms,\n      child: tmp.childList.length ? tmp.childList.map(function (item) {\n        return {\n          id: item.id,\n          pid: item.pid,\n          name: item.name,\n          url: item.component,\n          path: '/' + tmp.pid + '/' + item.pid + '/',\n          perms: item.perms,\n          extra: item.icon,\n          child: item.childList.length ? item.childList.map(function (item1) {\n            return {\n              id: item1.id,\n              pid: item1.pid,\n              name: item1.name,\n              url: item1.component,\n              path: '/' + tmp.pid + '/' + item.pid + '/' + item1.pid + '/',\n              perms: item1.perms,\n              extra: item1.icon,\n              child: item1.childList.length ? item1.childList.map(function (item2) {\n                return {\n                  id: item2.id,\n                  pid: item2.pid,\n                  name: item2.name,\n                  url: item2.component,\n                  path: '/' + tmp.pid + '/' + item.pid + '/' + item1.pid + '/' + item2.pid + '/',\n                  perms: item2.perms,\n                  extra: item2.icon\n                };\n              }) : []\n            };\n          }) : []\n        };\n      }) : [],\n      extra: tmp.icon\n    };\n    arr.push(obj);\n  });\n  return arr;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
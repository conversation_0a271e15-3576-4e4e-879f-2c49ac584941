{"ast": null, "code": "import _defineProperty from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nimport _objectSpread from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.trim.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/**\r\n * Created by PanJiaChen on 16/11/18.\r\n */\nvar baseAttr = {\n  min: '%s最小长度为:min',\n  max: '%s最大长度为:max',\n  length: '%s长度必须为:length',\n  range: '%s长度为:range',\n  pattern: '$s格式错误'\n};\n\n/**\r\n * @param {string} path\r\n * @returns {Boolean}\r\n */\nexport function isExternal(path) {\n  return /^(https?:|mailto:|tel:)/.test(path);\n}\n\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\nexport function validUsername(str) {\n  var valid_map = ['admin', 'editor'];\n  return valid_map.indexOf(str.trim()) >= 0;\n}\n\n/**\r\n * @param {string} url\r\n * @returns {Boolean}\r\n */\nexport function validURL(url) {\n  var reg = /^(https?|ftp):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$/;\n  return reg.test(url);\n}\n\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\nexport function validLowerCase(str) {\n  var reg = /^[a-z]+$/;\n  return reg.test(str);\n}\n\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\nexport function validUpperCase(str) {\n  var reg = /^[A-Z]+$/;\n  return reg.test(str);\n}\n\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\nexport function validAlphabets(str) {\n  var reg = /^[A-Za-z]+$/;\n  return reg.test(str);\n}\n\n/**\r\n * @param {string} email\r\n * @returns {Boolean}\r\n */\nexport function validEmail(email) {\n  // eslint-disable-next-line no-useless-escape\n  var reg = /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n  return reg.test(email);\n}\n\n/**\r\n * @param {string} str\r\n * @returns {Boolean}\r\n */\nexport function isString(str) {\n  if (typeof str === 'string' || str instanceof String) {\n    return true;\n  }\n  return false;\n}\n\n/**\r\n * @param {Array} arg\r\n * @returns {Boolean}\r\n */\nexport function isArray(arg) {\n  if (typeof Array.isArray === 'undefined') {\n    return Object.prototype.toString.call(arg) === '[object Array]';\n  }\n  return Array.isArray(arg);\n}\nvar bindMessage = function bindMessage(fn, message) {\n  fn.message = function (field) {\n    return message.replace('%s', field || '');\n  };\n};\nexport function required(message) {\n  var opt = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return _objectSpread({\n    required: true,\n    message: message,\n    type: 'string'\n  }, opt);\n}\nbindMessage(required, '请输入%s');\n\n/**\r\n * 正确的金额\r\n *\r\n * @param message\r\n * @returns {*}\r\n */\nexport function num(message) {\n  return attrs.pattern(/(^[1-9]([0-9]+)?(\\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\\.[0-9]([0-9])?$)/, message);\n}\nbindMessage(num, '%s格式不正确');\nvar attrs = Object.keys(baseAttr).reduce(function (attrs, key) {\n  attrs[key] = function (attr) {\n    var message = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    var opt = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var _attr = key === 'range' ? {\n      min: attr[0],\n      max: attr[1]\n    } : _defineProperty({}, key, attr);\n    return _objectSpread(_objectSpread({\n      message: message.replace(\":\".concat(key), key === 'range' ? \"\".concat(attr[0], \"-\").concat(attr[1]) : attr),\n      type: 'string'\n    }, _attr), opt);\n  };\n  bindMessage(attrs[key], baseAttr[key]);\n  return attrs;\n}, {});\nexport default attrs;\n\n/**\r\n * 函数防抖 (只执行最后一次点击)\r\n * @param fn\r\n * @param delay\r\n * @returns {Function}\r\n * @constructor\r\n */\nexport var Debounce = function Debounce(fn, t) {\n  var delay = t || 500;\n  var timer;\n  return function () {\n    var _this = this;\n    var args = arguments;\n    if (timer) {\n      clearTimeout(timer);\n    }\n    timer = setTimeout(function () {\n      timer = null;\n      fn.apply(_this, args);\n    }, delay);\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
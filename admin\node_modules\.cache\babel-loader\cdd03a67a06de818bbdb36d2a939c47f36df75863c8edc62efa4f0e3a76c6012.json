{"ast": null, "code": "/**\r\n * v-dialogDragWidth 可拖动弹窗高度（右下角）\r\n * Copyright (c) 2019 ruoyi\r\n */\n\nexport default {\n  bind: function bind(el) {\n    var dragDom = el.querySelector('.el-dialog');\n    var lineEl = document.createElement('div');\n    lineEl.style = 'width: 6px; background: inherit; height: 10px; position: absolute; right: 0; bottom: 0; margin: auto; z-index: 1; cursor: nwse-resize;';\n    lineEl.addEventListener('mousedown', function (e) {\n      // 鼠标按下，计算当前元素距离可视区的距离\n      var disX = e.clientX - el.offsetLeft;\n      var disY = e.clientY - el.offsetTop;\n      // 当前宽度 高度\n      var curWidth = dragDom.offsetWidth;\n      var curHeight = dragDom.offsetHeight;\n      document.onmousemove = function (e) {\n        e.preventDefault(); // 移动时禁用默认事件\n        // 通过事件委托，计算移动的距离\n        var xl = e.clientX - disX;\n        var yl = e.clientY - disY;\n        dragDom.style.width = \"\".concat(curWidth + xl, \"px\");\n        dragDom.style.height = \"\".concat(curHeight + yl, \"px\");\n      };\n      document.onmouseup = function (e) {\n        document.onmousemove = null;\n        document.onmouseup = null;\n      };\n    }, false);\n    dragDom.appendChild(lineEl);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
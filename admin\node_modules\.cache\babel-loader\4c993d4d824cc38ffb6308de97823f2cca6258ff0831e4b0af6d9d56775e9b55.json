{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nconst storeRouter = {\n  path: '/store',\n  component: Layout,\n  redirect: '/store/index',\n  name: 'Store',\n  meta: {\n    title: '商品',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'index',\n    component: () => import('@/views/store/index'),\n    name: 'StoreIndex',\n    meta: {\n      title: '商品管理',\n      icon: ''\n    }\n  }, {\n    path: 'sort',\n    component: () => import('@/views/store/sort/index'),\n    name: 'Sort',\n    meta: {\n      title: '商品分类',\n      icon: ''\n    }\n  }, {\n    path: 'attr',\n    component: () => import('@/views/store/storeAttr/index'),\n    name: 'SortAttr',\n    meta: {\n      title: '商品规格',\n      icon: ''\n    }\n  }, {\n    path: 'comment',\n    component: () => import('@/views/store/storeComment/index'),\n    name: 'StoreComment',\n    meta: {\n      title: '商品评论',\n      icon: ''\n    }\n  }, {\n    path: 'list/creatProduct/:id?/:isDisabled?',\n    component: () => import('@/views/store/creatStore/index'),\n    name: 'SortCreat',\n    meta: {\n      title: '商品添加',\n      noCache: true,\n      activeMenu: `/store/index`\n    },\n    hidden: true\n  }]\n};\nexport default storeRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
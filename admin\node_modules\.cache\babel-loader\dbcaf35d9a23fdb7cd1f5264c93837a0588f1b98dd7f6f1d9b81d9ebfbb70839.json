{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\nexport function login(data) {\n  return request({\n    url: '/admin/login',\n    method: 'post',\n    data\n  });\n}\nexport function getInfo(token) {\n  return request({\n    url: '/admin/getAdminInfoByToken',\n    method: 'get',\n    params: {\n      token\n    }\n  });\n}\nexport function logout() {\n  return request({\n    url: '/admin/logout',\n    method: 'get'\n  });\n}\n\n/**\r\n * 会员管理 列表\r\n * @param pram\r\n */\nexport function userListApi(params) {\n  return request({\n    url: `/admin/user/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员管理 修改\r\n * @param pram\r\n */\nexport function userUpdateApi(params, data) {\n  return request({\n    url: `/admin/user/update`,\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 会员管理等级 修改\r\n * @param pram\r\n */\nexport function userLevelUpdateApi(data) {\n  return request({\n    url: `/admin/user/update/level`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 会员管理 详情\r\n * @param pram\r\n */\nexport function userInfoApi(params) {\n  return request({\n    url: `/admin/user/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员管理 账户详情\r\n * @param pram\r\n */\nexport function infobyconditionApi(params) {\n  return request({\n    url: `/admin/user/infobycondition`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员管理 账户详情top数据\r\n * @param pram\r\n */\nexport function topdetailApi(params) {\n  return request({\n    url: `/admin/user/topdetail`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员管理 批量设置分组\r\n * @param pram\r\n */\nexport function groupPiApi(params) {\n  return request({\n    url: `/admin/user/group`,\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 会员管理 批量设置标签\r\n * @param pram\r\n */\nexport function tagPiApi(params) {\n  return request({\n    url: `/admin/user/tag`,\n    method: 'post',\n    params\n  });\n}\n\n/**\r\n * 会员管理 积分余额\r\n * @param pram\r\n */\nexport function foundsApi(params) {\n  return request({\n    url: `/admin/user/operate/founds`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员管理 删除\r\n * @param pram\r\n */\nexport function userDeleteApi(params) {\n  return request({\n    url: `/admin/user/delete`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员等级 列表\r\n * @param pram\r\n */\nexport function levelListApi() {\n  return request({\n    url: `/admin/system/user/level/list`,\n    method: 'get'\n  });\n}\n\n/**\r\n * 会员等级 新增\r\n * @param pram\r\n */\nexport function levelSaveApi(data) {\n  return request({\n    url: `/admin/system/user/level/save`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 会员等级 编辑\r\n *  @param pram\r\n */\nexport function levelUpdateApi(params, data) {\n  return request({\n    url: `/admin/system/user/level/update/${params}`,\n    method: 'post',\n    // params,\n    data\n  });\n}\n\n/**\r\n * 会员等级 详情\r\n * @param pram\r\n */\nexport function levelInfoApi(params) {\n  return request({\n    url: `/admin/system/user/level/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员等级 删除\r\n * @param pram\r\n */\nexport function levelDeleteApi(id) {\n  return request({\n    url: `/admin/system/user/level/delete/${id}`,\n    method: 'post'\n  });\n}\n\n/**\r\n * 会员等级 是否显示\r\n * @param pram\r\n */\nexport function levelUseApi(data) {\n  return request({\n    url: `/admin/system/user/level/use`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 会员标签 列表\r\n * @param pram\r\n */\nexport function tagListApi(params) {\n  return request({\n    url: `/admin/user/tag/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员标签 新增\r\n * @param pram\r\n */\nexport function tagSaveApi(data) {\n  return request({\n    url: `/admin/user/tag/save`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 会员标签 编辑\r\n * @param pram\r\n */\nexport function tagUpdateApi(params, data) {\n  return request({\n    url: `/admin/user/tag/update`,\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 会员标签 详情\r\n * @param pram\r\n */\nexport function tagInfoApi(params) {\n  return request({\n    url: `/admin/user/tag/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员标签 删除\r\n * @param pram\r\n */\nexport function tagDeleteApi(params) {\n  return request({\n    url: `/admin/user/tag/delete`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员分组 列表\r\n * @param pram\r\n */\nexport function groupListApi(params) {\n  return request({\n    url: `/admin/user/group/list`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员分组 新增\r\n * @param pram\r\n */\nexport function groupSaveApi(data) {\n  return request({\n    url: `/admin/user/group/save`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 会员分组 编辑\r\n * @param pram\r\n */\nexport function groupUpdateApi(params, data) {\n  return request({\n    url: `/admin/user/group/update`,\n    method: 'post',\n    params,\n    data\n  });\n}\n\n/**\r\n * 会员分组 详情\r\n * @param pram\r\n */\nexport function groupInfoApi(params) {\n  return request({\n    url: `/admin/user/group/info`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 会员分组 删除\r\n * @param pram\r\n */\nexport function groupDeleteApi(params) {\n  return request({\n    url: `/admin/user/group/delete`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n *获取登录页图片\r\n */\nexport function getLoginPicApi() {\n  return request({\n    url: `/admin/getLoginPic`,\n    method: 'get'\n  });\n}\n\n/**\r\n * @description 验证码\r\n */\nexport function captchaApi() {\n  return request({\n    url: `/admin/validate/code/get`,\n    method: 'get'\n  });\n}\n\n/**\r\n * @description 修改上级推广人\r\n */\nexport function updateSpreadApi(data) {\n  return request({\n    url: `/admin/user/update/spread`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * @description 修改手机号\r\n */\nexport function updatePhoneApi(params) {\n  return request({\n    url: `/admin/user/update/phone`,\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 查询是否需要开启图形验证码\r\n * @returns {*}\r\n */\nexport function captchaconfigApi() {\n  return request({\n    url: `/admin/validate/code/getcaptchaconfig`,\n    method: 'get'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar appSettingRouter = {\n  path: '/appSetting',\n  component: Layout,\n  redirect: '/appSetting/publicAccount/wxReply/follow',\n  name: 'appSetting',\n  meta: {\n    title: '应用',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'publicAccount',\n    name: 'publicAccount',\n    component: function component() {\n      return import('@/views/appSetting/wxAccount');\n    },\n    meta: {\n      title: '公众号',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'wxMenus',\n      component: function component() {\n        return import('@/views/appSetting/wxAccount/wxMenus');\n      },\n      name: 'wxMenus',\n      meta: {\n        title: '微信菜单',\n        icon: ''\n      }\n    }, {\n      path: 'wxReply',\n      component: function component() {\n        return import('@/views/appSetting/wxAccount/reply/index');\n      },\n      name: 'wxReply',\n      meta: {\n        title: '自动回复',\n        icon: ''\n      },\n      redirect: '/appSetting/publicAccount/wxReply/follow',\n      children: [{\n        path: 'follow',\n        component: function component() {\n          return import('@/views/appSetting/wxAccount/reply/follow');\n        },\n        name: 'wxFollow',\n        meta: {\n          title: '微信关注回复',\n          icon: ''\n        }\n      }, {\n        path: 'keyword',\n        component: function component() {\n          return import('@/views/appSetting/wxAccount/reply/keyword');\n        },\n        name: 'wxKeyword',\n        meta: {\n          title: '关键字回复',\n          icon: ''\n        }\n      }, {\n        path: 'replyIndex',\n        component: function component() {\n          return import('@/views/appSetting/wxAccount/reply/follow');\n        },\n        name: 'wxReplyIndex',\n        meta: {\n          title: '无效关键词回复',\n          icon: ''\n        }\n      }, {\n        path: 'keyword/save/:id?',\n        name: 'wechatKeywordAdd',\n        meta: {\n          title: '关键字添加',\n          noCache: true,\n          activeMenu: \"/appSetting/publicAccount/wxReply/keyword\"\n        },\n        hidden: true,\n        component: function component() {\n          return import('@/views/appSetting/wxAccount/reply/follow');\n        }\n      }]\n    }]\n  }]\n};\nexport default appSettingRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
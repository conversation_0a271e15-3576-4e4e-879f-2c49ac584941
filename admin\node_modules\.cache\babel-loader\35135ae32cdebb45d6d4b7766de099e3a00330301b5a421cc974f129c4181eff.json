{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\n\n/**\r\n * 角色详情\r\n */\nexport function getRoleById(pram) {\n  return request({\n    url: \"/admin/system/role/info/\".concat(pram.roles),\n    method: 'GET'\n  });\n}\n\n/**\r\n * 菜单\r\n * @param pram\r\n */\nexport function menuListApi() {\n  return request({\n    url: '/admin/getMenus',\n    method: 'GET'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
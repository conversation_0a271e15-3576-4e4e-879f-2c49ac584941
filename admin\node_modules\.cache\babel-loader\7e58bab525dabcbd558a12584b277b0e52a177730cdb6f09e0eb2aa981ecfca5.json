{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\nexport function systemConfigCheck(pram) {\n  const data = {\n    name: pram.name\n  };\n  return request({\n    url: '/admin/system/config/check',\n    method: 'GET',\n    params: data\n  });\n}\nexport function systemConfigInfo(pram) {\n  const data = {\n    formId: pram.id\n  };\n  return request({\n    url: '/admin/system/config/info',\n    method: 'GET',\n    params: data\n  });\n}\nexport function systemConfigSave(pram) {\n  return request({\n    url: '/admin/system/config/save/form',\n    method: 'POST',\n    data: pram\n  });\n}\n\n/**\r\n * 文件上传\r\n * @param data\r\n */\nexport function fileFileApi(data, params) {\n  return request({\n    url: '/admin/upload/file',\n    method: 'POST',\n    params,\n    data\n  });\n}\n\n/**\r\n * 图片上传\r\n * @param data\r\n */\nexport function fileImageApi(data, params) {\n  return request({\n    url: '/admin/upload/image',\n    method: 'POST',\n    params,\n    data\n  });\n}\n\n/**\r\n * 图片列表\r\n * @param data\r\n */\nexport function fileListApi(params) {\n  return request({\n    url: '/admin/system/attachment/list',\n    method: 'get',\n    params\n  });\n}\n\n/**\r\n * 图片列表 删除图片\r\n * @param data\r\n */\nexport function fileDeleteApi(id) {\n  return request({\n    url: `/admin/system/attachment/delete/${id}`,\n    method: 'get'\n  });\n}\n\n/**\r\n * 图片列表 移動分類\r\n * @param data\r\n */\nexport function attachmentMoveApi(data) {\n  return request({\n    url: `/admin/system/attachment/move`,\n    method: 'post',\n    data\n  });\n}\n\n/**\r\n * 微信上传图片\r\n * @param data\r\n */\nexport function wechatUploadApi(data, params) {\n  return request({\n    url: `/admin/wechat/media/upload`,\n    method: 'post',\n    data,\n    params\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[8580],{88580:function(e,t,n){n.r(t),n.d(t,{default:function(){return b}});var i=n(20641),a=n(53751),l=n(90033),o={class:"user-list"},s={class:"filter-container"},r={"slot-scope":"scope"},u={"slot-scope":"scope"},c={"slot-scope":"scope"},d={"slot-scope":"scope"},p={"slot-scope":"scope"},f={"slot-scope":"scope"};function m(e,t,n,m,g,h){var k=(0,i.g2)("el-input"),b=(0,i.g2)("el-button"),_=(0,i.g2)("el-table-column"),w=(0,i.g2)("el-tag"),y=(0,i.g2)("el-table"),L=(0,i.g2)("pagination"),v=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",o,[(0,i.Lk)("div",s,[(0,i.bF)(k,{modelValue:g.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=function(e){return g.listQuery.keyword=e}),placeholder:"请输入用户名或手机号",style:{width:"200px"},class:"filter-item",onKeyup:(0,a.jR)(h.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),(0,i.bF)(b,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:h.handleFilter},{default:(0,i.k6)(function(){return t[3]||(t[3]=[(0,i.eW)(" 搜索 ",-1)])}),_:1,__:[3]},8,["onClick"])]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(y,{data:g.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:(0,i.k6)(function(){return[(0,i.bF)(_,{align:"center",label:"ID",width:"95"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",r,[(0,i.eW)((0,l.v_)(e.scope.row.id),1)])]}),_:1}),(0,i.bF)(_,{label:"用户名"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",u,[(0,i.eW)((0,l.v_)(e.scope.row.username),1)])]}),_:1}),(0,i.bF)(_,{label:"手机号",width:"120"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",c,[(0,i.Lk)("span",null,(0,l.v_)(e.scope.row.phone),1)])]}),_:1}),(0,i.bF)(_,{label:"注册时间",width:"150"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",d,[t[4]||(t[4]=(0,i.Lk)("i",{class:"el-icon-time"},null,-1)),(0,i.Lk)("span",null,(0,l.v_)(e.scope.row.createTime),1)])]}),_:1}),(0,i.bF)(_,{label:"状态",width:"100"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",p,[(0,i.bF)(w,{type:1===e.scope.row.status?"success":"danger"},{default:(0,i.k6)(function(){return[(0,i.eW)((0,l.v_)(1===e.scope.row.status?"正常":"禁用"),1)]}),_:1},8,["type"])])]}),_:1}),(0,i.bF)(_,{label:"操作",align:"center",width:"200"},{default:(0,i.k6)(function(){return[(0,i.Lk)("template",f,[(0,i.bF)(b,{size:"mini",type:"primary",onClick:t[1]||(t[1]=function(t){return h.handleEdit(e.scope.row)})},{default:(0,i.k6)(function(){return t[5]||(t[5]=[(0,i.eW)(" 编辑 ",-1)])}),_:1,__:[5]}),(0,i.bF)(b,{size:"mini",type:1===e.scope.row.status?"danger":"success",onClick:t[2]||(t[2]=function(t){return h.handleStatus(e.scope.row)})},{default:(0,i.k6)(function(){return[(0,i.eW)((0,l.v_)(1===e.scope.row.status?"禁用":"启用"),1)]}),_:1},8,["type"])])]}),_:1})]}),_:1},8,["data"])),[[v,g.listLoading]]),(0,i.bo)((0,i.bF)(L,{total:g.total,page:g.listQuery.page,limit:g.listQuery.limit,onPagination:h.getList},null,8,["total","page","limit","onPagination"]),[[a.aG,g.total>0]])])}var g={name:"UserList",data:function(){return{list:[{id:1,username:"admin",phone:"13800138000",createTime:"2025-01-01 10:00:00",status:1},{id:2,username:"user001",phone:"13800138001",createTime:"2025-01-02 11:00:00",status:1}],total:2,listLoading:!1,listQuery:{page:1,limit:20,keyword:""}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.listLoading=!0,setTimeout(function(){e.listLoading=!1},500)},handleFilter:function(){this.listQuery.page=1,this.getList()},handleEdit:function(e){this.$message.info("编辑用户: "+e.username)},handleStatus:function(e){var t=1===e.status?"禁用":"启用";this.$message.info(t+"用户: "+e.username)}}},h=n(66262);const k=(0,h.A)(g,[["render",m],["__scopeId","data-v-7b6e4a0c"]]);var b=k}}]);
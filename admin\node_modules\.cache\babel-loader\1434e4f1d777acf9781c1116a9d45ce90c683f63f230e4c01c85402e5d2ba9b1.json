{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nconst mobileRouter = {\n  path: '/javaMobile',\n  component: Layout,\n  redirect: '/javaMobile/index',\n  name: 'Mobile',\n  alwaysShow: true,\n  meta: {\n    title: '移动端',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'orderCancellation',\n    component: () => import('@/views/mobile/orderCancellation/index.vue'),\n    name: 'OrderCancellation',\n    meta: {\n      title: '订单核销',\n      icon: ''\n    }\n  }, {\n    path: 'orderStatistics',\n    component: () => import('@/views/mobile/orderStatistics/index.vue'),\n    name: 'OrderStatistics',\n    meta: {\n      title: '订单统计'\n    }\n  }, {\n    path: 'orderList/:types?',\n    component: () => import('@/views/mobile/orderStatistics/orderList.vue'),\n    name: 'OrderList',\n    meta: {\n      title: '订单列表'\n    }\n  }, {\n    path: 'orderDelivery/:oid/:id?',\n    component: () => import('@/views/mobile/orderStatistics/orderDelivery.vue'),\n    name: 'OrderDelivery',\n    meta: {\n      title: '订单发货'\n    }\n  }, {\n    path: 'orderDetail/:id?/:goname?',\n    component: () => import('@/views/mobile/orderStatistics/orderDetail.vue'),\n    name: 'OrderDetail',\n    meta: {\n      title: '订单详情'\n    }\n  }, {\n    path: 'orderStatisticsDetail/:type/:time?',\n    component: () => import('@/views/mobile/orderStatistics/Statistics.vue'),\n    name: 'OrderStatisticsDetail',\n    meta: {\n      title: '订单数据统计'\n    }\n  }]\n};\nexport default mobileRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
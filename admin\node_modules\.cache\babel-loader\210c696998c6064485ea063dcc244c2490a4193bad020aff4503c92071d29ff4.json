{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, with<PERSON>eys as _withKeys, openBlock as _openBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, withModifiers as _withModifiers, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"svg-container\"\n};\nconst _hoisted_3 = {\n  class: \"svg-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_svg_icon = _resolveComponent(\"svg-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"loginForm\",\n    model: $data.loginForm,\n    rules: $data.loginRules,\n    class: \"login-form\",\n    \"auto-complete\": \"on\",\n    \"label-position\": \"left\"\n  }, {\n    default: _withCtx(() => [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n      class: \"title-container\"\n    }, [_createElementVNode(\"h3\", {\n      class: \"title\"\n    }, \"CRMEB管理后台\")], -1)), _createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_2, [_createVNode(_component_svg_icon, {\n        \"icon-class\": \"user\"\n      })]), _createVNode(_component_el_input, {\n        ref: \"username\",\n        modelValue: $data.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.loginForm.username = $event),\n        placeholder: \"用户名\",\n        name: \"username\",\n        type: \"text\",\n        tabindex: \"1\",\n        \"auto-complete\": \"on\"\n      }, null, 8, [\"modelValue\"])]),\n      _: 1\n    }), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"span\", _hoisted_3, [_createVNode(_component_svg_icon, {\n        \"icon-class\": \"password\"\n      })]), (_openBlock(), _createBlock(_component_el_input, {\n        key: $data.passwordType,\n        ref: \"password\",\n        modelValue: $data.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.loginForm.password = $event),\n        type: $data.passwordType,\n        placeholder: \"密码\",\n        name: \"password\",\n        tabindex: \"2\",\n        \"auto-complete\": \"on\",\n        onKeyup: _withKeys($options.handleLogin, [\"enter\", \"native\"])\n      }, null, 8, [\"modelValue\", \"type\", \"onKeyup\"])), _createElementVNode(\"span\", {\n        class: \"show-pwd\",\n        onClick: _cache[2] || (_cache[2] = (...args) => $options.showPwd && $options.showPwd(...args))\n      }, [_createVNode(_component_svg_icon, {\n        \"icon-class\": $data.passwordType === 'password' ? 'eye' : 'eye-open'\n      }, null, 8, [\"icon-class\"])])]),\n      _: 1\n    }), _createVNode(_component_el_button, {\n      loading: $data.loading,\n      type: \"primary\",\n      style: {\n        \"width\": \"100%\",\n        \"margin-bottom\": \"30px\"\n      },\n      onClick: _withModifiers($options.handleLogin, [\"prevent\"])\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" 登录 \", -1)])),\n      _: 1,\n      __: [3]\n    }, 8, [\"loading\", \"onClick\"])]),\n    _: 1,\n    __: [4]\n  }, 8, [\"model\", \"rules\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[137],{137:function(e,t,l){l.r(t),l.d(t,{default:function(){return y}});var a=l(641),o=l(3751),s=l(33);const i={class:"order-list"},r={class:"filter-container"},n={"slot-scope":"scope"},c={"slot-scope":"scope"},u={"slot-scope":"scope"},d={"slot-scope":"scope"},p={"slot-scope":"scope"},m={style:{color:"#f56c6c"}},b={"slot-scope":"scope"},g={"slot-scope":"scope"},h={"slot-scope":"scope"};function k(e,t,l,k,f,_){const w=(0,a.g2)("el-input"),y=(0,a.g2)("el-option"),F=(0,a.g2)("el-select"),L=(0,a.g2)("el-button"),v=(0,a.g2)("el-table-column"),W=(0,a.g2)("el-tag"),N=(0,a.g2)("el-table"),Q=(0,a.g2)("pagination"),P=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.Lk)("div",r,[(0,a.bF)(w,{modelValue:f.listQuery.orderNo,"onUpdate:modelValue":t[0]||(t[0]=e=>f.listQuery.orderNo=e),placeholder:"请输入订单号",style:{width:"200px"},class:"filter-item",onKeyup:(0,o.jR)(_.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),(0,a.bF)(F,{modelValue:f.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>f.listQuery.status=e),placeholder:"订单状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:(0,a.k6)(()=>[(0,a.bF)(y,{label:"待付款",value:"0"}),(0,a.bF)(y,{label:"待发货",value:"1"}),(0,a.bF)(y,{label:"待收货",value:"2"}),(0,a.bF)(y,{label:"已完成",value:"3"}),(0,a.bF)(y,{label:"已取消",value:"-1"})]),_:1},8,["modelValue"]),(0,a.bF)(L,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:_.handleFilter},{default:(0,a.k6)(()=>t[4]||(t[4]=[(0,a.eW)(" 搜索 ",-1)])),_:1,__:[4]},8,["onClick"])]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(N,{data:f.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:(0,a.k6)(()=>[(0,a.bF)(v,{align:"center",label:"订单ID",width:"100"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",n,[(0,a.eW)((0,s.v_)(e.scope.row.id),1)])]),_:1}),(0,a.bF)(v,{label:"订单号",width:"180"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",c,[(0,a.eW)((0,s.v_)(e.scope.row.orderNo),1)])]),_:1}),(0,a.bF)(v,{label:"用户",width:"120"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",u,[(0,a.eW)((0,s.v_)(e.scope.row.username),1)])]),_:1}),(0,a.bF)(v,{label:"商品信息","min-width":"200"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",d,[(0,a.eW)((0,s.v_)(e.scope.row.productInfo),1)])]),_:1}),(0,a.bF)(v,{label:"订单金额",width:"120"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",p,[(0,a.Lk)("span",m,"¥"+(0,s.v_)(e.scope.row.totalPrice),1)])]),_:1}),(0,a.bF)(v,{label:"订单状态",width:"100"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",b,[(0,a.bF)(W,{type:_.getStatusType(e.scope.row.status)},{default:(0,a.k6)(()=>[(0,a.eW)((0,s.v_)(_.getStatusText(e.scope.row.status)),1)]),_:1},8,["type"])])]),_:1}),(0,a.bF)(v,{label:"下单时间",width:"150"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",g,[t[5]||(t[5]=(0,a.Lk)("i",{class:"el-icon-time"},null,-1)),(0,a.Lk)("span",null,(0,s.v_)(e.scope.row.createTime),1)])]),_:1}),(0,a.bF)(v,{label:"操作",align:"center",width:"200"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",h,[(0,a.bF)(L,{size:"mini",type:"primary",onClick:t[2]||(t[2]=t=>_.handleDetail(e.scope.row))},{default:(0,a.k6)(()=>t[6]||(t[6]=[(0,a.eW)(" 详情 ",-1)])),_:1,__:[6]}),1===e.scope.row.status?((0,a.uX)(),(0,a.Wv)(L,{key:0,size:"mini",type:"success",onClick:t[3]||(t[3]=t=>_.handleShip(e.scope.row))},{default:(0,a.k6)(()=>t[7]||(t[7]=[(0,a.eW)(" 发货 ",-1)])),_:1,__:[7]})):(0,a.Q3)("",!0)])]),_:1})]),_:1},8,["data"])),[[P,f.listLoading]]),(0,a.bo)((0,a.bF)(Q,{total:f.total,page:f.listQuery.page,limit:f.listQuery.limit,onPagination:_.getList},null,8,["total","page","limit","onPagination"]),[[o.aG,f.total>0]])])}var f={name:"OrderList",data(){return{list:[{id:1,orderNo:"ORD202501010001",username:"user001",productInfo:"iPhone 15 Pro x1",totalPrice:7999,status:1,createTime:"2025-01-01 10:30:00"},{id:2,orderNo:"ORD202501010002",username:"user002",productInfo:"MacBook Pro x1",totalPrice:12999,status:0,createTime:"2025-01-01 11:00:00"}],total:2,listLoading:!1,listQuery:{page:1,limit:20,orderNo:"",status:""}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,setTimeout(()=>{this.listLoading=!1},500)},handleFilter(){this.listQuery.page=1,this.getList()},handleDetail(e){this.$message.info("查看订单详情: "+e.orderNo)},handleShip(e){this.$message.info("发货订单: "+e.orderNo)},getStatusType(e){const t={"-1":"info",0:"warning",1:"primary",2:"success",3:"success"};return t[e]||"info"},getStatusText(e){const t={"-1":"已取消",0:"待付款",1:"待发货",2:"待收货",3:"已完成"};return t[e]||"未知"}}},_=l(6262);const w=(0,_.A)(f,[["render",k],["__scopeId","data-v-7605ece7"]]);var y=w}}]);
{"ast": null, "code": "import _objectSpread from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { ElMessageBox, ElMessage, ElNotification, ElLoading } from 'element-plus';\nvar dialog = {\n  confirm: ElMessageBox.confirm,\n  alert: ElMessageBox.alert,\n  toast: ElMessage,\n  notify: ElNotification,\n  loading: ElLoading\n};\nvar icons = {\n  error: '操作失败',\n  success: '操作成功'\n};\nObject.keys(icons).reduce(function (dialog, key) {\n  dialog[key] = function (mes) {\n    var obj = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    return new Promise(function (resolve) {\n      ElMessage(_objectSpread({\n        message: mes || icons[key],\n        type: key,\n        duration: 1000,\n        onClose: function onClose() {\n          resolve();\n        }\n      }, obj));\n    });\n  };\n  return dialog;\n}, dialog);\ndialog.message = function () {\n  var mes = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '操作失败';\n  var obj = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return new Promise(function (resolve) {\n    ElMessage(_objectSpread({\n      message: mes,\n      duration: 1000,\n      onClose: function onClose() {\n        resolve();\n      }\n    }, obj));\n  });\n};\ndialog.validateError = function () {\n  validatorDefaultCatch.apply(void 0, arguments);\n};\nexport function validatorDefaultCatch(err) {\n  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'message';\n  console.log(err);\n  return dialog[type](err.errors[0].message);\n}\nexport default dialog;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
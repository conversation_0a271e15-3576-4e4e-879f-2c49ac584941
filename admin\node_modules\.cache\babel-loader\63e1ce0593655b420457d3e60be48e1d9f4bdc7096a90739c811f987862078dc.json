{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n//会员过滤器\n\n/**\r\n * 等级\r\n */\nexport function levelFilter(status) {\n  if (!status) {\n    return '';\n  }\n  var arrayList = JSON.parse(localStorage.getItem('single-admin-levelKey'));\n  var array = arrayList.filter(function (item) {\n    return status === item.id;\n  });\n  if (array.length) {\n    return array[0].name;\n  } else {\n    return '';\n  }\n}\n\n/**\r\n * 用户类型\r\n */\nexport function typeFilter(status) {\n  var statusMap = {\n    wechat: '微信用户',\n    routine: '小程序用户',\n    h5: 'H5用户'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * 用户类型\r\n */\nexport function filterIsPromoter(status) {\n  var statusMap = {\n    true: '推广员',\n    false: '普通用户'\n  };\n  return statusMap[status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.replace.js\";\n/**\r\n * v-dialogDrag 弹窗拖拽\r\n * Copyright (c) 2019 ruoyi\r\n */\n\nexport default {\n  bind: function bind(el, binding, vnode, oldVnode) {\n    var value = binding.value;\n    if (value == false) return;\n    // 获取拖拽内容头部\n    var dialogHeaderEl = el.querySelector('.el-dialog__header');\n    var dragDom = el.querySelector('.el-dialog');\n    dialogHeaderEl.style.cursor = 'move';\n    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);\n    var sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);\n    dragDom.style.position = 'absolute';\n    dragDom.style.marginTop = 0;\n    var width = dragDom.style.width;\n    if (width.includes('%')) {\n      width = +document.body.clientWidth * (+width.replace(/\\%/g, '') / 100);\n    } else {\n      width = +width.replace(/\\px/g, '');\n    }\n    dragDom.style.left = \"\".concat((document.body.clientWidth - width) / 2, \"px\");\n    // 鼠标按下事件\n    dialogHeaderEl.onmousedown = function (e) {\n      // 鼠标按下，计算当前元素距离可视区的距离 (鼠标点击位置距离可视窗口的距离)\n      var disX = e.clientX - dialogHeaderEl.offsetLeft;\n      var disY = e.clientY - dialogHeaderEl.offsetTop;\n\n      // 获取到的值带px 正则匹配替换\n      var styL, styT;\n\n      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px\n      if (sty.left.includes('%')) {\n        styL = +document.body.clientWidth * (+sty.left.replace(/\\%/g, '') / 100);\n        styT = +document.body.clientHeight * (+sty.top.replace(/\\%/g, '') / 100);\n      } else {\n        styL = +sty.left.replace(/\\px/g, '');\n        styT = +sty.top.replace(/\\px/g, '');\n      }\n\n      // 鼠标拖拽事件\n      document.onmousemove = function (e) {\n        // 通过事件委托，计算移动的距离 （开始拖拽至结束拖拽的距离）\n        var l = e.clientX - disX;\n        var t = e.clientY - disY;\n        var finallyL = l + styL;\n        var finallyT = t + styT;\n\n        // 移动当前元素\n        dragDom.style.left = \"\".concat(finallyL, \"px\");\n        dragDom.style.top = \"\".concat(finallyT, \"px\");\n      };\n      document.onmouseup = function (e) {\n        document.onmousemove = null;\n        document.onmouseup = null;\n      };\n    };\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
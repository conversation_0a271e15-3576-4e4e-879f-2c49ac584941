{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-wrapper\"\n};\nconst _hoisted_2 = {\n  class: \"sidebar-container\"\n};\nconst _hoisted_3 = {\n  class: \"main-container\"\n};\nconst _hoisted_4 = {\n  class: \"navbar\"\n};\nconst _hoisted_5 = {\n  class: \"navbar-right\"\n};\nconst _hoisted_6 = {\n  class: \"app-main\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_el_submenu = _resolveComponent(\"el-submenu\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_menu, {\n    \"default-active\": \"1\",\n    class: \"el-menu-vertical\",\n    \"background-color\": \"#304156\",\n    \"text-color\": \"#bfcbd9\",\n    \"active-text-color\": \"#409EFF\",\n    router: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"/dashboard\"\n    }, {\n      default: _withCtx(() => _cache[0] || (_cache[0] = [_createElementVNode(\"i\", {\n        class: \"el-icon-s-home\"\n      }, null, -1), _createElementVNode(\"span\", null, \"首页\", -1)])),\n      _: 1,\n      __: [0]\n    }), _createVNode(_component_el_submenu, {\n      index: \"store\"\n    }, {\n      default: _withCtx(() => [_cache[3] || (_cache[3] = _createElementVNode(\"template\", {\n        slot: \"title\"\n      }, [_createElementVNode(\"i\", {\n        class: \"el-icon-goods\"\n      }), _createElementVNode(\"span\", null, \"商品管理\")], -1)), _createVNode(_component_el_menu_item, {\n        index: \"/store\"\n      }, {\n        default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"商品列表\", -1)])),\n        _: 1,\n        __: [1]\n      }), _createVNode(_component_el_menu_item, {\n        index: \"/store/sort\"\n      }, {\n        default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"商品分类\", -1)])),\n        _: 1,\n        __: [2]\n      })]),\n      _: 1,\n      __: [3]\n    }), _createVNode(_component_el_submenu, {\n      index: \"order\"\n    }, {\n      default: _withCtx(() => [_cache[5] || (_cache[5] = _createElementVNode(\"template\", {\n        slot: \"title\"\n      }, [_createElementVNode(\"i\", {\n        class: \"el-icon-s-order\"\n      }), _createElementVNode(\"span\", null, \"订单管理\")], -1)), _createVNode(_component_el_menu_item, {\n        index: \"/order\"\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"订单列表\", -1)])),\n        _: 1,\n        __: [4]\n      })]),\n      _: 1,\n      __: [5]\n    }), _createVNode(_component_el_submenu, {\n      index: \"user\"\n    }, {\n      default: _withCtx(() => [_cache[7] || (_cache[7] = _createElementVNode(\"template\", {\n        slot: \"title\"\n      }, [_createElementVNode(\"i\", {\n        class: \"el-icon-user\"\n      }), _createElementVNode(\"span\", null, \"用户管理\")], -1)), _createVNode(_component_el_menu_item, {\n        index: \"/user/list\"\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"用户列表\", -1)])),\n        _: 1,\n        __: [6]\n      })]),\n      _: 1,\n      __: [7]\n    }), _createVNode(_component_el_submenu, {\n      index: \"marketing\"\n    }, {\n      default: _withCtx(() => [_cache[9] || (_cache[9] = _createElementVNode(\"template\", {\n        slot: \"title\"\n      }, [_createElementVNode(\"i\", {\n        class: \"el-icon-present\"\n      }), _createElementVNode(\"span\", null, \"营销管理\")], -1)), _createVNode(_component_el_menu_item, {\n        index: \"/marketing/coupon\"\n      }, {\n        default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"优惠券\", -1)])),\n        _: 1,\n        __: [8]\n      })]),\n      _: 1,\n      __: [9]\n    })]),\n    _: 1\n  })]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"navbar-title\"\n  }, \"CRMEB管理系统\", -1)), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_dropdown, null, {\n    default: _withCtx(() => [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n      class: \"el-dropdown-link\"\n    }, [_createTextVNode(\" 管理员\"), _createElementVNode(\"i\", {\n      class: \"el-icon-arrow-down el-icon--right\"\n    })], -1)), _createVNode(_component_el_dropdown_menu, {\n      slot: \"dropdown\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_dropdown_item, null, {\n        default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"退出登录\", -1)])),\n        _: 1,\n        __: [10]\n      })]),\n      _: 1\n    })]),\n    _: 1,\n    __: [11]\n  })])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_router_view)])])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
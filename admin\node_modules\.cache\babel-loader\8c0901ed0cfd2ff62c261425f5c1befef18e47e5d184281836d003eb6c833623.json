{"ast": null, "code": "import _objectSpread from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.trim.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia';\nimport { login as _login, logout, getInfo as _getInfo } from '@/api/user';\nimport { getToken, setToken, removeToken } from '@/utils/auth';\nimport router, { resetRouter } from '@/router';\nimport { isLoginApi } from '@/api/sms';\nimport Cookies from 'js-cookie';\nimport { ElLoading } from 'element-plus';\nimport * as roleApi from '@/api/roleApi.js';\nimport { formatFlatteningRoutes } from '@/utils/system.js';\nimport * as Auth from '@/libs/wechat';\nexport var useUserStore = defineStore('user', {\n  state: function state() {\n    return {\n      token: getToken(),\n      name: '',\n      avatar: '',\n      introduction: '',\n      roles: [],\n      isLogin: Cookies.get('isLogin'),\n      permissions: [],\n      captcha: {\n        captchaVerification: '',\n        secretKey: '',\n        token: ''\n      },\n      menuList: JSON.parse(localStorage.getItem('MerPlatAdmin_MenuList')) || [],\n      oneLvMenus: [],\n      oneLvRoutes: JSON.parse(localStorage.getItem('MerPlatAdmin_oneLvRoutes')) || [],\n      childMenuList: []\n    };\n  },\n  getters: {\n    isLoggedIn: function isLoggedIn(state) {\n      return !!state.token;\n    },\n    userRoles: function userRoles(state) {\n      return state.roles;\n    },\n    userPermissions: function userPermissions(state) {\n      return state.permissions;\n    }\n  },\n  actions: {\n    // 登录\n    login: function login(userInfo) {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var username, password, captchaVerification;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              username = userInfo.username, password = userInfo.password, captchaVerification = userInfo.captchaVerification;\n              return _context.a(2, new Promise(function (resolve, reject) {\n                _login({\n                  username: username.trim(),\n                  password: password,\n                  captchaVerification: captchaVerification\n                }).then(function (response) {\n                  var data = response.data;\n                  _this.token = data.token;\n                  setToken(data.token);\n                  resolve();\n                }).catch(function (error) {\n                  reject(error);\n                });\n              }));\n          }\n        }, _callee);\n      }))();\n    },\n    // 检查登录状态\n    isLogin: function isLogin(userInfo) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              return _context2.a(2, new Promise(function (resolve, reject) {\n                isLoginApi(userInfo).then(function (response) {\n                  resolve(response);\n                }).catch(function (error) {\n                  reject(error);\n                });\n              }));\n          }\n        }, _callee2);\n      }))();\n    },\n    // 获取用户信息\n    getInfo: function getInfo() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              return _context3.a(2, new Promise(function (resolve, reject) {\n                _getInfo(_this2.token).then(function (response) {\n                  var data = response.data;\n                  if (!data) {\n                    reject('Verification failed, please Login again.');\n                  }\n                  var roles = data.roles,\n                    name = data.name,\n                    avatar = data.avatar,\n                    introduction = data.introduction,\n                    permissions = data.permissions;\n                  if (!roles || roles.length <= 0) {\n                    reject('getInfo: roles must be a non-null array!');\n                  }\n                  _this2.roles = roles;\n                  _this2.name = name;\n                  _this2.avatar = avatar;\n                  _this2.introduction = introduction;\n                  _this2.permissions = permissions;\n                  resolve(data);\n                }).catch(function (error) {\n                  reject(error);\n                });\n              }));\n          }\n        }, _callee3);\n      }))();\n    },\n    // 退出登录\n    handleLogout: function handleLogout() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              return _context4.a(2, new Promise(function (resolve, reject) {\n                logout(_this3.token).then(function () {\n                  _this3.token = '';\n                  _this3.roles = [];\n                  _this3.permissions = [];\n                  _this3.isLogin = false;\n                  removeToken();\n                  resetRouter();\n\n                  // 清除本地存储\n                  localStorage.removeItem('MerPlatAdmin_MenuList');\n                  localStorage.removeItem('MerPlatAdmin_oneLvRoutes');\n                  Cookies.remove('isLogin');\n                  resolve();\n                }).catch(function (error) {\n                  reject(error);\n                });\n              }));\n          }\n        }, _callee4);\n      }))();\n    },\n    // 重置token\n    resetToken: function resetToken() {\n      var _this4 = this;\n      return new Promise(function (resolve) {\n        _this4.token = '';\n        _this4.roles = [];\n        _this4.permissions = [];\n        removeToken();\n        resolve();\n      });\n    },\n    // 设置token\n    setTokenAction: function setTokenAction(token) {\n      var _this5 = this;\n      return new Promise(function (resolve) {\n        _this5.token = token;\n        setToken(token);\n        resolve();\n      });\n    },\n    // 获取菜单\n    getMenus: function getMenus() {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var loadingInstance;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              loadingInstance = ElLoading.service({\n                fullscreen: true\n              });\n              return _context5.a(2, new Promise(function (resolve, reject) {\n                roleApi.getMenus().then(function (res) {\n                  var menuList = res.data || [];\n                  menuList = _this6.replaceChildListWithChildren(menuList);\n                  _this6.menuList = menuList;\n                  localStorage.setItem('MerPlatAdmin_MenuList', JSON.stringify(menuList));\n                  var oneLvRoutes = formatFlatteningRoutes(menuList);\n                  _this6.oneLvRoutes = oneLvRoutes;\n                  localStorage.setItem('MerPlatAdmin_oneLvRoutes', JSON.stringify(oneLvRoutes));\n                  loadingInstance.close();\n                  resolve(res);\n                }).catch(function (error) {\n                  loadingInstance.close();\n                  reject(error);\n                });\n              }));\n          }\n        }, _callee5);\n      }))();\n    },\n    // 设置验证码信息\n    setCaptcha: function setCaptcha(captcha) {\n      this.captcha = captcha;\n    },\n    // 设置登录状态\n    setIsLogin: function setIsLogin(isLogin) {\n      this.isLogin = isLogin;\n      Cookies.set('isLogin', isLogin);\n    },\n    // 设置菜单列表\n    setMenuList: function setMenuList(menuList) {\n      this.menuList = menuList;\n    },\n    // 设置一级菜单\n    setOneLvMenus: function setOneLvMenus(oneLvMenus) {\n      this.oneLvMenus = oneLvMenus;\n    },\n    // 设置一级路由\n    setOneLvRoute: function setOneLvRoute(oneLvRoutes) {\n      this.oneLvRoutes = oneLvRoutes;\n    },\n    // 设置子菜单列表\n    setChildMenuList: function setChildMenuList(list) {\n      this.childMenuList = list;\n    },\n    // 辅助方法：替换childList为children\n    replaceChildListWithChildren: function replaceChildListWithChildren(data) {\n      var _this7 = this;\n      if (Array.isArray(data)) {\n        return data.map(function (item) {\n          var newItem = _objectSpread({}, item);\n          if (newItem.childList && Array.isArray(newItem.childList)) {\n            newItem.children = _this7.replaceChildListWithChildren(newItem.childList);\n            delete newItem.childList;\n          }\n          return newItem;\n        });\n      }\n      return data;\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
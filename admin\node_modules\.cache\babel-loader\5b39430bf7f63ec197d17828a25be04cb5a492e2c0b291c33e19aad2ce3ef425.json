{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { createRouter, createWebHistory } from 'vue-router';\n\n/* Layout */\nimport Layout from '@/layout';\n\n/* Router Modules */\n// import componentsRouter from './modules/components'\nimport storeRouter from './modules/store';\nimport orderRouter from './modules/order';\nimport userRouter from './modules/user';\nimport distributionRouter from './modules/distribution';\nimport marketingRouter from './modules/marketing';\nimport financialRouter from './modules/financial';\nimport contentRouter from './modules/content';\nimport operationRouter from './modules/operation';\nimport appSettingRouter from './modules/appSetting';\nimport maintainRouter from './modules/maintain';\nimport mobileRouter from './modules/mobile';\nimport designRouter from './modules/design';\nimport notificationRouter from './modules/notification';\n\n/**\r\n * Note: sub-menu only appear when route children.length >= 1\r\n * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html\r\n *\r\n * hidden: true                   if set true, item will not show in the sidebar(default is false)\r\n * alwaysShow: true               if set true, will always show the root menu\r\n *                                if not set alwaysShow, when item has more than one children route,\r\n *                                it will becomes nested mode, otherwise not show the root menu\r\n * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb\r\n * name:'router-name'             the name is used by <keep-alive> (must set!!!)\r\n * meta : {\r\n    roles: ['admin','editor']    control the page roles (you can set multiple roles)\r\n    title: 'title'               the name show in sidebar and breadcrumb (recommend set)\r\n    icon: 'svg-name'             the icon show in the sidebar\r\n    noCache: true                if set true, the page will no be cached(default is false)\r\n    affix: true                  if set true, the tag will affix in the tags-view\r\n    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)\r\n    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set\r\n  }\r\n */\n\n/**\r\n * constantRoutes\r\n * a base page that does not have permission requirements\r\n * all roles can be accessed\r\n *\r\n */\n\nexport var constantRoutes = [\n// 商品\nstoreRouter,\n// 订单\norderRouter,\n// 会员\nuserRouter,\n// 分销\ndistributionRouter,\n// 营销\nmarketingRouter,\n// 财务\nfinancialRouter,\n// 内容\ncontentRouter,\n// 通知\nnotificationRouter,\n// 设置\noperationRouter,\n// 应用\nappSettingRouter,\n// 维护\nmaintainRouter,\n//移动端管理\nmobileRouter,\n//装修\ndesignRouter, {\n  path: '/404',\n  component: function component() {\n    return import('@/views/error-page/404');\n  },\n  hidden: true\n}, {\n  path: '/page/design/creatDevise/:id?/:type?',\n  component: function component() {\n    return import('@/views/design/devise/creatDevise');\n  },\n  hidden: true\n}, {\n  path: '/redirect',\n  component: Layout,\n  hidden: true,\n  children: [{\n    path: '/redirect/:path(.*)',\n    component: function component() {\n      return import('@/views/redirect/index');\n    }\n  }]\n}, {\n  path: '/auth-send',\n  component: function component() {\n    return import('@/views/mobile/auth-send');\n  },\n  hidden: true\n}, {\n  path: '/login',\n  component: function component() {\n    return import('@/views/login/index');\n  },\n  hidden: true\n}, {\n  path: '/auth-redirect',\n  component: function component() {\n    return import('@/views/login/auth-redirect');\n  },\n  hidden: true\n}, {\n  path: '/401',\n  component: function component() {\n    return import('@/views/error-page/401');\n  },\n  hidden: true\n}, {\n  path: '/',\n  component: Layout,\n  redirect: '/dashboard',\n  children: [{\n    path: '/dashboard',\n    component: function component() {\n      return import('@/views/dashboard/index');\n    },\n    name: 'Dashboard',\n    meta: {\n      title: '主页',\n      icon: 'dashboard',\n      isAffix: true\n    }\n  }]\n}, {\n  path: '/setting/uploadPicture',\n  component: function component() {\n    return import('@/components/uploadPicture/index.vue');\n  },\n  name: 'uploadPicture'\n},\n// 404 page must be placed at the end !!!\n{\n  path: '/:pathMatch(.*)*',\n  redirect: '/404',\n  hidden: true\n}];\n\n/**\r\n * asyncRoutes\r\n * the routes that need to be dynamically loaded based on user roles\r\n */\nexport var asyncRoutes = [];\nvar router = createRouter({\n  history: createWebHistory(),\n  scrollBehavior: function scrollBehavior() {\n    return {\n      top: 0\n    };\n  },\n  routes: constantRoutes\n});\n\n// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465\nexport function resetRouter() {\n  // Vue Router 4 doesn't have matcher property\n  // Router reset functionality needs to be implemented differently\n  // This is a placeholder for now\n}\nexport default router;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
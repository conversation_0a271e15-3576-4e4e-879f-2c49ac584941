{"ast": null, "code": "export default {\n  name: 'TimeSelect',\n  props: {\n    modelValue: {\n      type: String,\n      default: ''\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    },\n    placeholder: {\n      type: String,\n      default: '选择时间'\n    }\n  },\n  emits: ['update:modelValue', 'change'],\n  methods: {\n    handleChange(value) {\n      this.$emit('update:modelValue', value);\n      this.$emit('change', value);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
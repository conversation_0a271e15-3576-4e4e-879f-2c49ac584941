[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# cat crmeb.log
nohup: ignoring input
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:41.684",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
  ,ad8888ba,  88888888ba  88b           d88 88888888888 88888888ba
 d8"'    `"8b 88      "8b 888b         d888 88          88      "8b
d8'           88      ,8P 88`8b       d8'88 88          88      ,8P
88            88aaaaaa8P' 88 `8b     d8' 88 88aaaaa     88aaaaaa8P'
88            88""""88'   88  `8b   d8'  88 88"""""     88""""""8b,
Y8,           88    `8b   88   `8b d8'   88 88          88      `8b
 Y8a.    .a8P 88     `8b  88    `888'    88 88          88      a8P
  `"Y8888Y"'  88      `8b 88     `8'     88 88888888888 88888888P"
文档地址:        https://help.crmeb.net/crmeb_java/1748037
演示站WEBPC：    https://admin.java.crmeb.ne
演示站H5:        https://java.crmeb.net

{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.483",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.zbkj.front.CrmebFrontApplication",
                    "message": "Starting CrmebFrontApplication on iZ0jlg3g6983s5cydi5llmZ with PID 496283 (/www/wwwroot/crmeb/Crmeb-front.jar started by root in /www/wwwroot/crmeb)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.484",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.zbkj.front.CrmebFrontApplication",
                    "message": "The following profiles are active: prod" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.485",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.831",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.832",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.832",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.832",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.832",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/classes!/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:42.833",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4667ae56" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:48.084",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.m.spring.mapper.ClassPathMapperScanner",
                    "message": "No MyBatis mapper was found in '[com.zbkj.front]' package. Please check your configuration." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:48.864",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:48.912",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:48.982",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 26ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.208",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /www/wwwroot/crmeb/Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.208",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: /www/wwwroot/crmeb/Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.209",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.304",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 8081 (http)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.334",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-8081"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.336",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:53.336",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:54.601",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:54.888",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:54.888",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:54.888",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 12055 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.873",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.874",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.953",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.954",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.955",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.957",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.957",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:56.958",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:01:59.207",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.zbkj.common.vo.UserFundsMonitor." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:00.704",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.zbkj.common.model.order.StoreOrderStatus." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:03.457",
                    "level": "WARN",
                    "thread": "main",
                    "class": "c.b.m.core.metadata.TableInfoHelper",
                    "message": "Warn: Could not find @TableId in Class: com.zbkj.common.model.system.SystemRoleMenu." }
                    
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.3.1 
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:06.325",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:06.956",
                    "level": "WARN",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ArticleFrontController': Unsatisfied dependency expressed through field 'articleService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'articleServiceImpl': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryServiceImpl': Unsatisfied dependency expressed through field 'systemAttachmentService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemAttachmentServiceImpl': Unsatisfied dependency expressed through field 'systemConfigService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:06.972",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} closing ..." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:07.003",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} closed" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:07.016",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Stopping service [Tomcat]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:07.033",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/www/wwwroot/crmeb/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-08-22 02:02:07.069",
                    "level": "ERROR",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Application run failed" }
                    
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ArticleFrontController': Unsatisfied dependency expressed through field 'articleService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'articleServiceImpl': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryServiceImpl': Unsatisfied dependency expressed through field 'systemAttachmentService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemAttachmentServiceImpl': Unsatisfied dependency expressed through field 'systemConfigService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
        at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
        at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
        at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
        at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
        at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:882)
        at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:878)
        at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:550)
        at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
        at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
        at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
        at com.zbkj.front.CrmebFrontApplication.main(CrmebFrontApplication.java:34)
        at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.lang.reflect.Method.invoke(Method.java:498)
        at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:109)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
        at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'articleServiceImpl': Unsatisfied dependency expressed through field 'categoryService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryServiceImpl': Unsatisfied dependency expressed through field 'systemAttachmentService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemAttachmentServiceImpl': Unsatisfied dependency expressed through field 'systemConfigService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
        at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
        at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
        at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
        at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
        at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
        at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1290)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1210)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
        ... 27 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'categoryServiceImpl': Unsatisfied dependency expressed through field 'systemAttachmentService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemAttachmentServiceImpl': Unsatisfied dependency expressed through field 'systemConfigService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
        at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
        at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
        at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
        at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
        at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
        at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1290)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1210)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
        ... 40 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemAttachmentServiceImpl': Unsatisfied dependency expressed through field 'systemConfigService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
        at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
        at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
        at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
        at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
        at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
        at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1290)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1210)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
        ... 53 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'systemConfigServiceImpl': Invocation of init method failed; nested exception is org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:416)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1788)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
        at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
        at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
        at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
        at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
        at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
        at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1290)
        at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1210)
        at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
        ... 66 common frames omitted
Caused by: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
### The error may exist in com/zbkj/service/dao/SystemConfigDao.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT   name,value   FROM eb_system_config     WHERE (status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
        at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
        at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
        at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
        at com.sun.proxy.$Proxy136.selectList(Unknown Source)
        at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
        at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:177)
        at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
        at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:96)
        at com.sun.proxy.$Proxy140.selectList(Unknown Source)
        at com.zbkj.service.service.impl.SystemConfigServiceImpl.loadingConfigCache(SystemConfigServiceImpl.java:400)
        at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.lang.reflect.Method.invoke(Method.java:498)
        at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
        at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333)
        at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157)
        ... 78 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Table 'crmeb.eb_system_config' doesn't exist
        at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
        at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
        at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
        at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
        at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
        at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
        at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
        at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:67)
        at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324)
        at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
        at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:163)
        at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:90)
        at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
        at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
        at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.lang.reflect.Method.invoke(Method.java:498)
        at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
        ... 92 common frames omitted
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# java -version
openjdk version "1.8.0_462"
OpenJDK Runtime Environment (build 1.8.0_462-b08)
OpenJDK 64-Bit Server VM (build 25.462-b08, mixed mode)
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# which java
/usr/bin/java
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# mysql -u crmeb -p'@Ckr015410' -h localhost -e "USE crmeb; SHOW TABLES;"
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# redis-cli ping
PONG
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# redis-cli -p 6379 ping
PONG
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# netstat -tlnp | grep 8081
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# lsof -i :8081
[root@iZ0jlg3g6983s5cydi5llmZ crmeb]# 
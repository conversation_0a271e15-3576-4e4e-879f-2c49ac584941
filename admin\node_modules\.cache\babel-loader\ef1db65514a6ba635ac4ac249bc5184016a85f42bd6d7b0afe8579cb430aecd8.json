{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar operationRouter = {\n  path: '/operation',\n  component: Layout,\n  redirect: '/operation/setting',\n  name: 'Operation',\n  meta: {\n    title: '设置',\n    icon: 'clipboard',\n    roles: ['admin']\n  },\n  children: [{\n    path: 'setting',\n    name: 'setting',\n    component: function component() {\n      return import('@/views/systemSetting/setting');\n    },\n    meta: {\n      title: '系统设置',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'notification',\n    name: 'notification',\n    component: function component() {\n      return import('@/views/systemSetting/notification');\n    },\n    meta: {\n      title: '消息通知',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'onePass',\n    name: 'onePass',\n    component: function component() {\n      return import('@/views/sms/smsConfig/index');\n    },\n    meta: {\n      title: '一号通',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'onePassConfig',\n    name: 'onePassConfig',\n    component: function component() {\n      return import('@/views/sms/smsConfig/config');\n    },\n    meta: {\n      title: '一号通配置',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'roleManager',\n    name: 'RoleManager',\n    component: function component() {\n      return import('@/views/systemSetting/administratorAuthority');\n    },\n    meta: {\n      title: '管理权限',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'identityManager',\n      component: function component() {\n        return import('@/views/systemSetting/administratorAuthority/identityManager');\n      },\n      name: 'identityManager',\n      meta: {\n        title: '角色管理',\n        icon: ''\n      }\n    }, {\n      path: 'adminList',\n      component: function component() {\n        return import('@/views/systemSetting/administratorAuthority/adminList');\n      },\n      name: 'adminList',\n      meta: {\n        title: '管理员列表',\n        icon: ''\n      }\n    }, {\n      path: 'promiseRules',\n      component: function component() {\n        return import('@/views/systemSetting/administratorAuthority/permissionRules');\n      },\n      name: 'promiseRules',\n      meta: {\n        title: '权限规则',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'systemSms',\n    component: function component() {\n      return import('@/views/sms');\n    },\n    name: 'systemSms',\n    meta: {\n      title: '短信设置',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'config',\n      component: function component() {\n        return import('@/views/sms/smsConfig');\n      },\n      name: 'SmsConfig',\n      meta: {\n        title: '短信账户',\n        noCache: true\n      }\n    }, {\n      path: 'template',\n      component: function component() {\n        return import('@/views/sms/smsTemplate');\n      },\n      name: 'SmsTemplate',\n      meta: {\n        title: '短信模板',\n        noCache: true,\n        activeMenu: \"/operation/onePass\"\n      }\n    }, {\n      path: 'pay',\n      component: function component() {\n        return import('@/views/sms/smsPay');\n      },\n      name: 'SmsPay',\n      meta: {\n        title: '短信购买',\n        noCache: true,\n        activeMenu: \"/operation/onePass\"\n      }\n    }, {\n      path: 'message',\n      component: function component() {\n        return import('@/views/sms/smsMessage');\n      },\n      name: 'SmsMessage',\n      meta: {\n        title: '短信开关',\n        noCache: true\n      }\n    }]\n  }, {\n    path: 'deliverGoods',\n    name: 'deliverGoods',\n    alwaysShow: true,\n    component: function component() {\n      return import('@/views/systemSetting/deliverGoods');\n    },\n    meta: {\n      title: '发货设置',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'takeGoods',\n      component: function component() {\n        return import('@/views/systemSetting/deliverGoods/takeGoods');\n      },\n      name: 'takeGoods',\n      meta: {\n        title: '提货设置',\n        noCache: true,\n        roles: ['admin']\n      },\n      redirect: '/operation/deliverGoods/takeGoods/collateUser',\n      children: [{\n        path: 'deliveryAddress',\n        component: function component() {\n          return import('@/views/systemSetting/deliverGoods/takeGoods/deliveryAddress');\n        },\n        name: 'deliveryAddress',\n        meta: {\n          title: '提货点',\n          icon: ''\n        }\n      }, {\n        path: 'collateOrder',\n        component: function component() {\n          return import('@/views/systemSetting/deliverGoods/takeGoods/collateOrder');\n        },\n        name: 'collateOrder',\n        meta: {\n          title: '核销订单',\n          icon: ''\n        }\n      }, {\n        path: 'collateUser',\n        component: function component() {\n          return import('@/views/systemSetting/deliverGoods/takeGoods/collateUser');\n        },\n        name: 'collateUser',\n        meta: {\n          title: '核销员',\n          icon: ''\n        }\n      }]\n    }, {\n      path: 'freightSet',\n      component: function component() {\n        return import('@/views/systemSetting/deliverGoods/freightSet');\n      },\n      name: 'freightSet',\n      meta: {\n        title: '运费模板',\n        noCache: true\n      }\n    }]\n  }]\n};\nexport default operationRouter; //collate", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
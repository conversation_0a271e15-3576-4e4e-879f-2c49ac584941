"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[1463],{1463:function(e,r,n){n.r(r),n.d(r,{default:function(){return i}});var t=n(641);const o={class:"error-page"},c={class:"error-content"};function u(e,r,n,u,a,s){const l=(0,t.g2)("el-button");return(0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",c,[r[1]||(r[1]=(0,t.Lk)("h1",null,"404",-1)),r[2]||(r[2]=(0,t.Lk)("h2",null,"页面未找到",-1)),r[3]||(r[3]=(0,t.Lk)("p",null,"抱歉，您访问的页面不存在",-1)),(0,t.bF)(l,{type:"primary",onClick:s.goHome},{default:(0,t.k6)(()=>r[0]||(r[0]=[(0,t.eW)("返回首页",-1)])),_:1,__:[0]},8,["onClick"])])])}n(4114);var a={name:"Error404",methods:{goHome(){this.$router.push("/")}}},s=n(6262);const l=(0,s.A)(a,[["render",u],["__scopeId","data-v-14743c59"]]);var i=l}}]);
{"ast": null, "code": "import \"core-js/modules/es.symbol.to-primitive.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.date.to-primitive.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
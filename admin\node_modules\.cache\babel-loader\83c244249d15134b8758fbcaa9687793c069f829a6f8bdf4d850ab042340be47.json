{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nvar sessionCache = {\n  set: function set(key, value) {\n    if (!sessionStorage) {\n      return;\n    }\n    if (key != null && value != null) {\n      sessionStorage.setItem(key, value);\n    }\n  },\n  get: function get(key) {\n    if (!sessionStorage) {\n      return null;\n    }\n    if (key == null) {\n      return null;\n    }\n    return sessionStorage.getItem(key);\n  },\n  setJSON: function setJSON(key, jsonValue) {\n    if (jsonValue != null) {\n      this.set(key, JSON.stringify(jsonValue));\n    }\n  },\n  getJSON: function getJSON(key) {\n    var value = this.get(key);\n    if (value != null) {\n      return JSON.parse(value);\n    }\n  },\n  remove: function remove(key) {\n    sessionStorage.removeItem(key);\n  }\n};\nvar localCache = {\n  set: function set(key, value) {\n    if (!localStorage) {\n      return;\n    }\n    if (key != null && value != null) {\n      localStorage.setItem(key, value);\n    }\n  },\n  get: function get(key) {\n    if (!localStorage) {\n      return null;\n    }\n    if (key == null) {\n      return null;\n    }\n    return localStorage.getItem(key);\n  },\n  setJSON: function setJSON(key, jsonValue) {\n    if (jsonValue != null) {\n      this.set(key, JSON.stringify(jsonValue));\n    }\n  },\n  getJSON: function getJSON(key) {\n    var value = this.get(key);\n    if (value != null) {\n      return JSON.parse(value);\n    }\n  },\n  remove: function remove(key) {\n    localStorage.removeItem(key);\n  },\n  // 检测缓存是否存在\n  has: function has(key) {\n    return localStorage.getItem(key) ? true : false;\n  },\n  setItem: function setItem(params) {\n    var obj = {\n      name: '',\n      value: '',\n      expires: '',\n      startTime: new Date().getTime()\n    };\n    var options = {};\n    //将obj和传进来的params合并\n    Object.assign(options, obj, params);\n    if (options.expires) {\n      //如果options.expires设置了的话\n      //以options.name为key，options为值放进去\n      localStorage.setItem(options.name, JSON.stringify(options));\n    } else {\n      //如果options.expires没有设置，就判断一下value的类型\n      var type = Object.prototype.toString.call(options.value);\n      //如果value是对象或者数组对象的类型，就先用JSON.stringify转一下，再存进去\n      if (Object.prototype.toString.call(options.value) == '[object Object]') {\n        options.value = JSON.stringify(options.value);\n      }\n      if (Object.prototype.toString.call(options.value) == '[object Array]') {\n        options.value = JSON.stringify(options.value);\n      }\n      localStorage.setItem(options.name, options.value);\n    }\n  }\n};\nexport default {\n  /**\r\n   * 会话级缓存\r\n   */\n  session: sessionCache,\n  /**\r\n   * 本地缓存\r\n   */\n  local: localCache\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
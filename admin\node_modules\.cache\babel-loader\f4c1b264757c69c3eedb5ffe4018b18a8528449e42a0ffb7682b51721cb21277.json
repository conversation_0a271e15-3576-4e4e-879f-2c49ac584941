{"ast": null, "code": "export default {\n  name: 'OrderList',\n  data() {\n    return {\n      list: [{\n        id: 1,\n        orderNo: 'ORD202501010001',\n        username: 'user001',\n        productInfo: 'iPhone 15 Pro x1',\n        totalPrice: 7999.00,\n        status: 1,\n        createTime: '2025-01-01 10:30:00'\n      }, {\n        id: 2,\n        orderNo: 'ORD202501010002',\n        username: 'user002',\n        productInfo: 'MacBook Pro x1',\n        totalPrice: 12999.00,\n        status: 0,\n        createTime: '2025-01-01 11:00:00'\n      }],\n      total: 2,\n      listLoading: false,\n      listQuery: {\n        page: 1,\n        limit: 20,\n        orderNo: '',\n        status: ''\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList() {\n      this.listLoading = true;\n      // 这里调用API获取订单列表\n      setTimeout(() => {\n        this.listLoading = false;\n      }, 500);\n    },\n    handleFilter() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleDetail(row) {\n      this.$message.info('查看订单详情: ' + row.orderNo);\n    },\n    handleShip(row) {\n      this.$message.info('发货订单: ' + row.orderNo);\n    },\n    getStatusType(status) {\n      const statusMap = {\n        '-1': 'info',\n        '0': 'warning',\n        '1': 'primary',\n        '2': 'success',\n        '3': 'success'\n      };\n      return statusMap[status] || 'info';\n    },\n    getStatusText(status) {\n      const statusMap = {\n        '-1': '已取消',\n        '0': '待付款',\n        '1': '待发货',\n        '2': '待收货',\n        '3': '已完成'\n      };\n      return statusMap[status] || '未知';\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
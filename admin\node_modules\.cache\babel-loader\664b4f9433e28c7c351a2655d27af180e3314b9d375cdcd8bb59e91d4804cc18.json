{"ast": null, "code": "import _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _typeof from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.from.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.global-this.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.map.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.object.values.js\";\nimport \"core-js/modules/es.reflect.get.js\";\nimport \"core-js/modules/es.reflect.set.js\";\nimport \"core-js/modules/es.reflect.to-string-tag.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.set.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.starts-with.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.self.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\n/*!\n * pinia v2.3.1\n * (c) 2025 Eduardo San Martin Morote\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isVue2, isRef, isReactive, set, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, del, nextTick, computed, toRefs } from 'vue-demi';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nvar activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nvar setActivePinia = function setActivePinia(pinia) {\n  return activePinia = pinia;\n};\n/**\n * Get the currently active pinia if there is any.\n */\nvar getActivePinia = function getActivePinia() {\n  return hasInjectionContext() && inject(piniaSymbol) || activePinia;\n};\nvar piniaSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia') : /* istanbul ignore next */Symbol();\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n  return o && _typeof(o) === 'object' && Object.prototype.toString.call(o) === '[object Object]' && typeof o.toJSON !== 'function';\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n  /**\n   * Direct mutation of the state:\n   *\n   * - `store.name = 'new name'`\n   * - `store.$state.name = 'new name'`\n   * - `store.list.push('new item')`\n   */\n  MutationType[\"direct\"] = \"direct\";\n  /**\n   * Mutated the state with `$patch` and an object\n   *\n   * - `store.$patch({ name: 'newName' })`\n   */\n  MutationType[\"patchObject\"] = \"patch object\";\n  /**\n   * Mutated the state with `$patch` and a function\n   *\n   * - `store.$patch(state => state.name = 'newName')`\n   */\n  MutationType[\"patchFunction\"] = \"patch function\";\n  // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\nvar IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nvar _global = /*#__PURE__*/function () {\n  return (typeof window === \"undefined\" ? \"undefined\" : _typeof(window)) === 'object' && window.window === window ? window : (typeof self === \"undefined\" ? \"undefined\" : _typeof(self)) === 'object' && self.self === self ? self : (typeof global === \"undefined\" ? \"undefined\" : _typeof(global)) === 'object' && global.global === global ? global : (typeof globalThis === \"undefined\" ? \"undefined\" : _typeof(globalThis)) === 'object' ? globalThis : {\n    HTMLElement: null\n  };\n}();\nfunction bom(blob) {\n  var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    _ref$autoBom = _ref.autoBom,\n    autoBom = _ref$autoBom === void 0 ? false : _ref$autoBom;\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xfeff), blob], {\n      type: blob.type\n    });\n  }\n  return blob;\n}\nfunction download(url, name, opts) {\n  var xhr = new XMLHttpRequest();\n  xhr.open('GET', url);\n  xhr.responseType = 'blob';\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts);\n  };\n  xhr.onerror = function () {\n    console.error('could not download file');\n  };\n  xhr.send();\n}\nfunction corsEnabled(url) {\n  var xhr = new XMLHttpRequest();\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false);\n  try {\n    xhr.send();\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'));\n  } catch (e) {\n    var evt = document.createEvent('MouseEvents');\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n    node.dispatchEvent(evt);\n  }\n}\nvar _navigator = (typeof navigator === \"undefined\" ? \"undefined\" : _typeof(navigator)) === 'object' ? navigator : {\n  userAgent: ''\n};\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nvar isMacOSWebView = /*#__PURE__*/function () {\n  return /Macintosh/.test(_navigator.userAgent) && /AppleWebKit/.test(_navigator.userAgent) && !/Safari/.test(_navigator.userAgent);\n}();\nvar saveAs = !IS_CLIENT ? function () {} // noop\n:\n// Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\ntypeof HTMLAnchorElement !== 'undefined' && 'download' in HTMLAnchorElement.prototype && !isMacOSWebView ? downloadSaveAs :\n// Use msSaveOrOpenBlob as a second approach\n'msSaveOrOpenBlob' in _navigator ? msSaveAs :\n// Fallback to using FileReader and a popup\nfileSaverSaveAs;\nfunction downloadSaveAs(blob) {\n  var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'download';\n  var opts = arguments.length > 2 ? arguments[2] : undefined;\n  var a = document.createElement('a');\n  a.download = name;\n  a.rel = 'noopener'; // tabnabbing\n  // TODO: detect chrome extensions & packaged apps\n  // a.target = '_blank'\n  if (typeof blob === 'string') {\n    // Support regular links\n    a.href = blob;\n    if (a.origin !== location.origin) {\n      if (corsEnabled(a.href)) {\n        download(blob, name, opts);\n      } else {\n        a.target = '_blank';\n        click(a);\n      }\n    } else {\n      click(a);\n    }\n  } else {\n    // Support blobs\n    a.href = URL.createObjectURL(blob);\n    setTimeout(function () {\n      URL.revokeObjectURL(a.href);\n    }, 4e4); // 40s\n    setTimeout(function () {\n      click(a);\n    }, 0);\n  }\n}\nfunction msSaveAs(blob) {\n  var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'download';\n  var opts = arguments.length > 2 ? arguments[2] : undefined;\n  if (typeof blob === 'string') {\n    if (corsEnabled(blob)) {\n      download(blob, name, opts);\n    } else {\n      var a = document.createElement('a');\n      a.href = blob;\n      a.target = '_blank';\n      setTimeout(function () {\n        click(a);\n      });\n    }\n  } else {\n    // @ts-ignore: works on windows\n    navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n  }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n  // Open a popup immediately do go around popup blocker\n  // Mostly only available on user interaction and the fileReader is async so...\n  popup = popup || open('', '_blank');\n  if (popup) {\n    popup.document.title = popup.document.body.innerText = 'downloading...';\n  }\n  if (typeof blob === 'string') return download(blob, name, opts);\n  var force = blob.type === 'application/octet-stream';\n  var isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n  var isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n  if ((isChromeIOS || force && isSafari || isMacOSWebView) && typeof FileReader !== 'undefined') {\n    // Safari doesn't allow downloading of blob URLs\n    var reader = new FileReader();\n    reader.onloadend = function () {\n      var url = reader.result;\n      if (typeof url !== 'string') {\n        popup = null;\n        throw new Error('Wrong reader.result type');\n      }\n      url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n      if (popup) {\n        popup.location.href = url;\n      } else {\n        location.assign(url);\n      }\n      popup = null; // reverse-tabnabbing #460\n    };\n    reader.readAsDataURL(blob);\n  } else {\n    var url = URL.createObjectURL(blob);\n    if (popup) popup.location.assign(url);else location.href = url;\n    popup = null; // reverse-tabnabbing #460\n    setTimeout(function () {\n      URL.revokeObjectURL(url);\n    }, 4e4); // 40s\n  }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n  var piniaMessage = '🍍 ' + message;\n  if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n    // No longer available :(\n    __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n  } else if (type === 'error') {\n    console.error(piniaMessage);\n  } else if (type === 'warn') {\n    console.warn(piniaMessage);\n  } else {\n    console.log(piniaMessage);\n  }\n}\nfunction isPinia(o) {\n  return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n  if (!('clipboard' in navigator)) {\n    toastMessage(\"Your browser doesn't support the Clipboard API\", 'error');\n    return true;\n  }\n}\nfunction checkNotFocusedError(error) {\n  if (error instanceof Error && error.message.toLowerCase().includes('document is not focused')) {\n    toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n    return true;\n  }\n  return false;\n}\nfunction actionGlobalCopyState(_x) {\n  return _actionGlobalCopyState.apply(this, arguments);\n}\nfunction _actionGlobalCopyState() {\n  _actionGlobalCopyState = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(pinia) {\n    var _t5;\n    return _regenerator().w(function (_context4) {\n      while (1) switch (_context4.p = _context4.n) {\n        case 0:\n          if (!checkClipboardAccess()) {\n            _context4.n = 1;\n            break;\n          }\n          return _context4.a(2);\n        case 1:\n          _context4.p = 1;\n          _context4.n = 2;\n          return navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n        case 2:\n          toastMessage('Global state copied to clipboard.');\n          _context4.n = 5;\n          break;\n        case 3:\n          _context4.p = 3;\n          _t5 = _context4.v;\n          if (!checkNotFocusedError(_t5)) {\n            _context4.n = 4;\n            break;\n          }\n          return _context4.a(2);\n        case 4:\n          toastMessage(\"Failed to serialize the state. Check the console for more details.\", 'error');\n          console.error(_t5);\n        case 5:\n          return _context4.a(2);\n      }\n    }, _callee4, null, [[1, 3]]);\n  }));\n  return _actionGlobalCopyState.apply(this, arguments);\n}\nfunction actionGlobalPasteState(_x2) {\n  return _actionGlobalPasteState.apply(this, arguments);\n}\nfunction _actionGlobalPasteState() {\n  _actionGlobalPasteState = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(pinia) {\n    var _t6, _t7, _t8, _t9, _t0, _t1;\n    return _regenerator().w(function (_context5) {\n      while (1) switch (_context5.p = _context5.n) {\n        case 0:\n          if (!checkClipboardAccess()) {\n            _context5.n = 1;\n            break;\n          }\n          return _context5.a(2);\n        case 1:\n          _context5.p = 1;\n          _t6 = loadStoresState;\n          _t7 = pinia;\n          _t8 = JSON;\n          _context5.n = 2;\n          return navigator.clipboard.readText();\n        case 2:\n          _t9 = _context5.v;\n          _t0 = _t8.parse.call(_t8, _t9);\n          _t6(_t7, _t0);\n          toastMessage('Global state pasted from clipboard.');\n          _context5.n = 5;\n          break;\n        case 3:\n          _context5.p = 3;\n          _t1 = _context5.v;\n          if (!checkNotFocusedError(_t1)) {\n            _context5.n = 4;\n            break;\n          }\n          return _context5.a(2);\n        case 4:\n          toastMessage(\"Failed to deserialize the state from clipboard. Check the console for more details.\", 'error');\n          console.error(_t1);\n        case 5:\n          return _context5.a(2);\n      }\n    }, _callee5, null, [[1, 3]]);\n  }));\n  return _actionGlobalPasteState.apply(this, arguments);\n}\nfunction actionGlobalSaveState(_x3) {\n  return _actionGlobalSaveState.apply(this, arguments);\n}\nfunction _actionGlobalSaveState() {\n  _actionGlobalSaveState = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(pinia) {\n    return _regenerator().w(function (_context6) {\n      while (1) switch (_context6.n) {\n        case 0:\n          try {\n            saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n              type: 'text/plain;charset=utf-8'\n            }), 'pinia-state.json');\n          } catch (error) {\n            toastMessage(\"Failed to export the state as JSON. Check the console for more details.\", 'error');\n            console.error(error);\n          }\n        case 1:\n          return _context6.a(2);\n      }\n    }, _callee6);\n  }));\n  return _actionGlobalSaveState.apply(this, arguments);\n}\nvar fileInput;\nfunction getFileOpener() {\n  if (!fileInput) {\n    fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.json';\n  }\n  function openFile() {\n    return new Promise(function (resolve, reject) {\n      fileInput.onchange = /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var files, file, _t, _t2, _t3, _t4;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              files = fileInput.files;\n              if (files) {\n                _context.n = 1;\n                break;\n              }\n              return _context.a(2, resolve(null));\n            case 1:\n              file = files.item(0);\n              if (file) {\n                _context.n = 2;\n                break;\n              }\n              return _context.a(2, resolve(null));\n            case 2:\n              _t = resolve;\n              _context.n = 3;\n              return file.text();\n            case 3:\n              _t2 = _context.v;\n              _t3 = file;\n              _t4 = {\n                text: _t2,\n                file: _t3\n              };\n              return _context.a(2, _t(_t4));\n          }\n        }, _callee);\n      }));\n      // @ts-ignore: TODO: changed from 4.3 to 4.4\n      fileInput.oncancel = function () {\n        return resolve(null);\n      };\n      fileInput.onerror = reject;\n      fileInput.click();\n    });\n  }\n  return openFile;\n}\nfunction actionGlobalOpenStateFile(_x4) {\n  return _actionGlobalOpenStateFile.apply(this, arguments);\n}\nfunction _actionGlobalOpenStateFile() {\n  _actionGlobalOpenStateFile = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7(pinia) {\n    var _open, result, text, file, _t10;\n    return _regenerator().w(function (_context7) {\n      while (1) switch (_context7.p = _context7.n) {\n        case 0:\n          _context7.p = 0;\n          _open = getFileOpener();\n          _context7.n = 1;\n          return _open();\n        case 1:\n          result = _context7.v;\n          if (result) {\n            _context7.n = 2;\n            break;\n          }\n          return _context7.a(2);\n        case 2:\n          text = result.text, file = result.file;\n          loadStoresState(pinia, JSON.parse(text));\n          toastMessage(\"Global state imported from \\\"\".concat(file.name, \"\\\".\"));\n          _context7.n = 4;\n          break;\n        case 3:\n          _context7.p = 3;\n          _t10 = _context7.v;\n          toastMessage(\"Failed to import the state from JSON. Check the console for more details.\", 'error');\n          console.error(_t10);\n        case 4:\n          return _context7.a(2);\n      }\n    }, _callee7, null, [[0, 3]]);\n  }));\n  return _actionGlobalOpenStateFile.apply(this, arguments);\n}\nfunction loadStoresState(pinia, state) {\n  for (var key in state) {\n    var storeState = pinia.state.value[key];\n    // store is already instantiated, patch it\n    if (storeState) {\n      Object.assign(storeState, state[key]);\n    } else {\n      // store is not instantiated, set the initial state\n      pinia.state.value[key] = state[key];\n    }\n  }\n}\nfunction formatDisplay(display) {\n  return {\n    _custom: {\n      display: display\n    }\n  };\n}\nvar PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nvar PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n  return isPinia(store) ? {\n    id: PINIA_ROOT_ID,\n    label: PINIA_ROOT_LABEL\n  } : {\n    id: store.$id,\n    label: store.$id\n  };\n}\nfunction formatStoreForInspectorState(store) {\n  if (isPinia(store)) {\n    var storeNames = Array.from(store._s.keys());\n    var storeMap = store._s;\n    var _state = {\n      state: storeNames.map(function (storeId) {\n        return {\n          editable: true,\n          key: storeId,\n          value: store.state.value[storeId]\n        };\n      }),\n      getters: storeNames.filter(function (id) {\n        return storeMap.get(id)._getters;\n      }).map(function (id) {\n        var store = storeMap.get(id);\n        return {\n          editable: false,\n          key: id,\n          value: store._getters.reduce(function (getters, key) {\n            getters[key] = store[key];\n            return getters;\n          }, {})\n        };\n      })\n    };\n    return _state;\n  }\n  var state = {\n    state: Object.keys(store.$state).map(function (key) {\n      return {\n        editable: true,\n        key: key,\n        value: store.$state[key]\n      };\n    })\n  };\n  // avoid adding empty getters\n  if (store._getters && store._getters.length) {\n    state.getters = store._getters.map(function (getterName) {\n      return {\n        editable: false,\n        key: getterName,\n        value: store[getterName]\n      };\n    });\n  }\n  if (store._customProperties.size) {\n    state.customProperties = Array.from(store._customProperties).map(function (key) {\n      return {\n        editable: true,\n        key: key,\n        value: store[key]\n      };\n    });\n  }\n  return state;\n}\nfunction formatEventData(events) {\n  if (!events) return {};\n  if (Array.isArray(events)) {\n    // TODO: handle add and delete for arrays and objects\n    return events.reduce(function (data, event) {\n      data.keys.push(event.key);\n      data.operations.push(event.type);\n      data.oldValue[event.key] = event.oldValue;\n      data.newValue[event.key] = event.newValue;\n      return data;\n    }, {\n      oldValue: {},\n      keys: [],\n      operations: [],\n      newValue: {}\n    });\n  } else {\n    return {\n      operation: formatDisplay(events.type),\n      key: formatDisplay(events.key),\n      oldValue: events.oldValue,\n      newValue: events.newValue\n    };\n  }\n}\nfunction formatMutationType(type) {\n  switch (type) {\n    case MutationType.direct:\n      return 'mutation';\n    case MutationType.patchFunction:\n      return '$patch';\n    case MutationType.patchObject:\n      return '$patch';\n    default:\n      return 'unknown';\n  }\n}\n\n// timeline can be paused when directly changing the state\nvar isTimelineActive = true;\nvar componentStateTypes = [];\nvar MUTATIONS_LAYER_ID = 'pinia:mutations';\nvar INSPECTOR_ID = 'pinia';\nvar assign$1 = Object.assign;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nvar getStoreType = function getStoreType(id) {\n  return '🍍 ' + id;\n};\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes: componentStateTypes,\n    app: app\n  }, function (api) {\n    if (typeof api.now !== 'function') {\n      toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n    }\n    api.addTimelineLayer({\n      id: MUTATIONS_LAYER_ID,\n      label: \"Pinia \\uD83C\\uDF4D\",\n      color: 0xe5df88\n    });\n    api.addInspector({\n      id: INSPECTOR_ID,\n      label: 'Pinia 🍍',\n      icon: 'storage',\n      treeFilterPlaceholder: 'Search stores',\n      actions: [{\n        icon: 'content_copy',\n        action: function action() {\n          actionGlobalCopyState(pinia);\n        },\n        tooltip: 'Serialize and copy the state'\n      }, {\n        icon: 'content_paste',\n        action: function () {\n          var _action = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n            return _regenerator().w(function (_context2) {\n              while (1) switch (_context2.n) {\n                case 0:\n                  _context2.n = 1;\n                  return actionGlobalPasteState(pinia);\n                case 1:\n                  api.sendInspectorTree(INSPECTOR_ID);\n                  api.sendInspectorState(INSPECTOR_ID);\n                case 2:\n                  return _context2.a(2);\n              }\n            }, _callee2);\n          }));\n          function action() {\n            return _action.apply(this, arguments);\n          }\n          return action;\n        }(),\n        tooltip: 'Replace the state with the content of your clipboard'\n      }, {\n        icon: 'save',\n        action: function action() {\n          actionGlobalSaveState(pinia);\n        },\n        tooltip: 'Save the state as a JSON file'\n      }, {\n        icon: 'folder_open',\n        action: function () {\n          var _action2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n            return _regenerator().w(function (_context3) {\n              while (1) switch (_context3.n) {\n                case 0:\n                  _context3.n = 1;\n                  return actionGlobalOpenStateFile(pinia);\n                case 1:\n                  api.sendInspectorTree(INSPECTOR_ID);\n                  api.sendInspectorState(INSPECTOR_ID);\n                case 2:\n                  return _context3.a(2);\n              }\n            }, _callee3);\n          }));\n          function action() {\n            return _action2.apply(this, arguments);\n          }\n          return action;\n        }(),\n        tooltip: 'Import the state from a JSON file'\n      }],\n      nodeActions: [{\n        icon: 'restore',\n        tooltip: 'Reset the state (with \"$reset\")',\n        action: function action(nodeId) {\n          var store = pinia._s.get(nodeId);\n          if (!store) {\n            toastMessage(\"Cannot reset \\\"\".concat(nodeId, \"\\\" store because it wasn't found.\"), 'warn');\n          } else if (typeof store.$reset !== 'function') {\n            toastMessage(\"Cannot reset \\\"\".concat(nodeId, \"\\\" store because it doesn't have a \\\"$reset\\\" method implemented.\"), 'warn');\n          } else {\n            store.$reset();\n            toastMessage(\"Store \\\"\".concat(nodeId, \"\\\" reset.\"));\n          }\n        }\n      }]\n    });\n    api.on.inspectComponent(function (payload, ctx) {\n      var proxy = payload.componentInstance && payload.componentInstance.proxy;\n      if (proxy && proxy._pStores) {\n        var piniaStores = payload.componentInstance.proxy._pStores;\n        Object.values(piniaStores).forEach(function (store) {\n          payload.instanceData.state.push({\n            type: getStoreType(store.$id),\n            key: 'state',\n            editable: true,\n            value: store._isOptionsAPI ? {\n              _custom: {\n                value: toRaw(store.$state),\n                actions: [{\n                  icon: 'restore',\n                  tooltip: 'Reset the state of this store',\n                  action: function action() {\n                    return store.$reset();\n                  }\n                }]\n              }\n            } :\n            // NOTE: workaround to unwrap transferred refs\n            Object.keys(store.$state).reduce(function (state, key) {\n              state[key] = store.$state[key];\n              return state;\n            }, {})\n          });\n          if (store._getters && store._getters.length) {\n            payload.instanceData.state.push({\n              type: getStoreType(store.$id),\n              key: 'getters',\n              editable: false,\n              value: store._getters.reduce(function (getters, key) {\n                try {\n                  getters[key] = store[key];\n                } catch (error) {\n                  // @ts-expect-error: we just want to show it in devtools\n                  getters[key] = error;\n                }\n                return getters;\n              }, {})\n            });\n          }\n        });\n      }\n    });\n    api.on.getInspectorTree(function (payload) {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        var stores = [pinia];\n        stores = stores.concat(Array.from(pinia._s.values()));\n        payload.rootNodes = (payload.filter ? stores.filter(function (store) {\n          return '$id' in store ? store.$id.toLowerCase().includes(payload.filter.toLowerCase()) : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase());\n        }) : stores).map(formatStoreForInspectorTree);\n      }\n    });\n    // Expose pinia instance as $pinia to window\n    globalThis.$pinia = pinia;\n    api.on.getInspectorState(function (payload) {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        var inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          // this could be the selected store restored for a different project\n          // so it's better not to say anything here\n          return;\n        }\n        if (inspectedStore) {\n          // Expose selected store as $store to window\n          if (payload.nodeId !== PINIA_ROOT_ID) globalThis.$store = toRaw(inspectedStore);\n          payload.state = formatStoreForInspectorState(inspectedStore);\n        }\n      }\n    });\n    api.on.editInspectorState(function (payload, ctx) {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        var inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          return toastMessage(\"store \\\"\".concat(payload.nodeId, \"\\\" not found\"), 'error');\n        }\n        var path = payload.path;\n        if (!isPinia(inspectedStore)) {\n          // access only the state\n          if (path.length !== 1 || !inspectedStore._customProperties.has(path[0]) || path[0] in inspectedStore.$state) {\n            path.unshift('$state');\n          }\n        } else {\n          // Root access, we can omit the `.value` because the devtools API does it for us\n          path.unshift('state');\n        }\n        isTimelineActive = false;\n        payload.set(inspectedStore, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n    api.on.editComponentState(function (payload) {\n      if (payload.type.startsWith('🍍')) {\n        var storeId = payload.type.replace(/^🍍\\s*/, '');\n        var store = pinia._s.get(storeId);\n        if (!store) {\n          return toastMessage(\"store \\\"\".concat(storeId, \"\\\" not found\"), 'error');\n        }\n        var path = payload.path;\n        if (path[0] !== 'state') {\n          return toastMessage(\"Invalid path for store \\\"\".concat(storeId, \"\\\":\\n\").concat(path, \"\\nOnly state can be modified.\"));\n        }\n        // rewrite the first entry to be able to directly set the state as\n        // well as any other path\n        path[0] = '$state';\n        isTimelineActive = false;\n        payload.set(store, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n  });\n}\nfunction addStoreToDevtools(app, store) {\n  if (!componentStateTypes.includes(getStoreType(store.$id))) {\n    componentStateTypes.push(getStoreType(store.$id));\n  }\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes: componentStateTypes,\n    app: app,\n    settings: {\n      logStoreChanges: {\n        label: 'Notify about new/deleted stores',\n        type: 'boolean',\n        defaultValue: true\n      }\n      // useEmojis: {\n      //   label: 'Use emojis in messages ⚡️',\n      //   type: 'boolean',\n      //   defaultValue: true,\n      // },\n    }\n  }, function (api) {\n    // gracefully handle errors\n    var now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n    store.$onAction(function (_ref2) {\n      var after = _ref2.after,\n        onError = _ref2.onError,\n        name = _ref2.name,\n        args = _ref2.args;\n      var groupId = runningActionId++;\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🛫 ' + name,\n          subtitle: 'start',\n          data: {\n            store: formatDisplay(store.$id),\n            action: formatDisplay(name),\n            args: args\n          },\n          groupId: groupId\n        }\n      });\n      after(function (result) {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            title: '🛬 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args: args,\n              result: result\n            },\n            groupId: groupId\n          }\n        });\n      });\n      onError(function (error) {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            logType: 'error',\n            title: '💥 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args: args,\n              error: error\n            },\n            groupId: groupId\n          }\n        });\n      });\n    }, true);\n    store._customProperties.forEach(function (name) {\n      watch(function () {\n        return unref(store[name]);\n      }, function (newValue, oldValue) {\n        api.notifyComponentUpdate();\n        api.sendInspectorState(INSPECTOR_ID);\n        if (isTimelineActive) {\n          api.addTimelineEvent({\n            layerId: MUTATIONS_LAYER_ID,\n            event: {\n              time: now(),\n              title: 'Change',\n              subtitle: name,\n              data: {\n                newValue: newValue,\n                oldValue: oldValue\n              },\n              groupId: activeAction\n            }\n          });\n        }\n      }, {\n        deep: true\n      });\n    });\n    store.$subscribe(function (_ref3, state) {\n      var events = _ref3.events,\n        type = _ref3.type;\n      api.notifyComponentUpdate();\n      api.sendInspectorState(INSPECTOR_ID);\n      if (!isTimelineActive) return;\n      // rootStore.state[store.id] = state\n      var eventData = {\n        time: now(),\n        title: formatMutationType(type),\n        data: assign$1({\n          store: formatDisplay(store.$id)\n        }, formatEventData(events)),\n        groupId: activeAction\n      };\n      if (type === MutationType.patchFunction) {\n        eventData.subtitle = '⤵️';\n      } else if (type === MutationType.patchObject) {\n        eventData.subtitle = '🧩';\n      } else if (events && !Array.isArray(events)) {\n        eventData.subtitle = events.type;\n      }\n      if (events) {\n        eventData.data['rawEvent(s)'] = {\n          _custom: {\n            display: 'DebuggerEvent',\n            type: 'object',\n            tooltip: 'raw DebuggerEvent[]',\n            value: events\n          }\n        };\n      }\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: eventData\n      });\n    }, {\n      detached: true,\n      flush: 'sync'\n    });\n    var hotUpdate = store._hotUpdate;\n    store._hotUpdate = markRaw(function (newStore) {\n      hotUpdate(newStore);\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🔥 ' + store.$id,\n          subtitle: 'HMR update',\n          data: {\n            store: formatDisplay(store.$id),\n            info: formatDisplay(\"HMR update\")\n          }\n        }\n      });\n      // update the devtools too\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n    });\n    var $dispose = store.$dispose;\n    store.$dispose = function () {\n      $dispose();\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n      api.getSettings().logStoreChanges && toastMessage(\"Disposed \\\"\".concat(store.$id, \"\\\" store \\uD83D\\uDDD1\"));\n    };\n    // trigger an update so it can display new registered stores\n    api.notifyComponentUpdate();\n    api.sendInspectorTree(INSPECTOR_ID);\n    api.sendInspectorState(INSPECTOR_ID);\n    api.getSettings().logStoreChanges && toastMessage(\"\\\"\".concat(store.$id, \"\\\" store installed \\uD83C\\uDD95\"));\n  });\n}\nvar runningActionId = 0;\nvar activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n  // original actions of the store as they are given by pinia. We are going to override them\n  var actions = actionNames.reduce(function (storeActions, actionName) {\n    // use toRaw to avoid tracking #541\n    storeActions[actionName] = toRaw(store)[actionName];\n    return storeActions;\n  }, {});\n  var _loop = function _loop(actionName) {\n    store[actionName] = function () {\n      // the running action id is incremented in a before action hook\n      var _actionId = runningActionId;\n      var trackedStore = wrapWithProxy ? new Proxy(store, {\n        get: function get() {\n          activeAction = _actionId;\n          return Reflect.get.apply(Reflect, arguments);\n        },\n        set: function set() {\n          activeAction = _actionId;\n          return Reflect.set.apply(Reflect, arguments);\n        }\n      }) : store;\n      // For Setup Stores we need https://github.com/tc39/proposal-async-context\n      activeAction = _actionId;\n      var retValue = actions[actionName].apply(trackedStore, arguments);\n      // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n      activeAction = undefined;\n      return retValue;\n    };\n  };\n  for (var actionName in actions) {\n    _loop(actionName);\n  }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin(_ref4) {\n  var app = _ref4.app,\n    store = _ref4.store,\n    options = _ref4.options;\n  // HMR module\n  if (store.$id.startsWith('__hot:')) {\n    return;\n  }\n  // detect option api vs setup api\n  store._isOptionsAPI = !!options.state;\n  // Do not overwrite actions mocked by @pinia/testing (#2298)\n  if (!store._p._testing) {\n    patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n    // Upgrade the HMR to also update the new actions\n    var originalHotUpdate = store._hotUpdate;\n    toRaw(store)._hotUpdate = function (newStore) {\n      originalHotUpdate.apply(this, arguments);\n      patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n    };\n  }\n  addStoreToDevtools(app,\n  // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n  store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n  var scope = effectScope(true);\n  // NOTE: here we could check the window object for a state and directly set it\n  // if there is anything like it with Vue 3 SSR\n  var state = scope.run(function () {\n    return ref({});\n  });\n  var _p = [];\n  // plugins added before calling app.use(pinia)\n  var toBeInstalled = [];\n  var pinia = markRaw({\n    install: function install(app) {\n      // this allows calling useStore() outside of a component setup after\n      // installing pinia's plugin\n      setActivePinia(pinia);\n      if (!isVue2) {\n        pinia._a = app;\n        app.provide(piniaSymbol, pinia);\n        app.config.globalProperties.$pinia = pinia;\n        /* istanbul ignore else */\n        if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n          registerPiniaDevtools(app, pinia);\n        }\n        toBeInstalled.forEach(function (plugin) {\n          return _p.push(plugin);\n        });\n        toBeInstalled = [];\n      }\n    },\n    use: function use(plugin) {\n      if (!this._a && !isVue2) {\n        toBeInstalled.push(plugin);\n      } else {\n        _p.push(plugin);\n      }\n      return this;\n    },\n    _p: _p,\n    // it's actually undefined here\n    // @ts-expect-error\n    _a: null,\n    _e: scope,\n    _s: new Map(),\n    state: state\n  });\n  // pinia devtools rely on dev only features so they cannot be forced unless\n  // the dev build of Vue is used. Avoid old browsers like IE11.\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT && typeof Proxy !== 'undefined') {\n    pinia.use(devtoolsPlugin);\n  }\n  return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n  pinia._e.stop();\n  pinia._s.clear();\n  pinia._p.splice(0);\n  pinia.state.value = {};\n  // @ts-expect-error: non valid\n  pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nvar isUseStore = function isUseStore(fn) {\n  return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n  // no need to go through symbols because they cannot be serialized anyway\n  for (var key in oldState) {\n    var subPatch = oldState[key];\n    // skip the whole sub tree\n    if (!(key in newState)) {\n      continue;\n    }\n    var targetValue = newState[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && !isRef(subPatch) && !isReactive(subPatch)) {\n      newState[key] = patchObject(targetValue, subPatch);\n    } else {\n      // objects are either a bit more complex (e.g. refs) or primitives, so we\n      // just set the whole thing\n      if (isVue2) {\n        set(newState, key, subPatch);\n      } else {\n        newState[key] = subPatch;\n      }\n    }\n  }\n  return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n  // strip as much as possible from iife.prod\n  if (!(process.env.NODE_ENV !== 'production')) {\n    return function () {};\n  }\n  return function (newModule) {\n    var pinia = hot.data.pinia || initialUseStore._pinia;\n    if (!pinia) {\n      // this store is still not used\n      return;\n    }\n    // preserve the pinia instance across loads\n    hot.data.pinia = pinia;\n    // console.log('got data', newStore)\n    for (var exportName in newModule) {\n      var useStore = newModule[exportName];\n      // console.log('checking for', exportName)\n      if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n        // console.log('Accepting update for', useStore.$id)\n        var id = useStore.$id;\n        if (id !== initialUseStore.$id) {\n          console.warn(\"The id of the store changed from \\\"\".concat(initialUseStore.$id, \"\\\" to \\\"\").concat(id, \"\\\". Reloading.\"));\n          // return import.meta.hot.invalidate()\n          return hot.invalidate();\n        }\n        var existingStore = pinia._s.get(id);\n        if (!existingStore) {\n          console.log(\"[Pinia]: skipping hmr because store doesn't exist yet\");\n          return;\n        }\n        useStore(pinia, existingStore);\n      }\n    }\n  };\n}\nvar noop = function noop() {};\nfunction addSubscription(subscriptions, callback, detached) {\n  var onCleanup = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : noop;\n  subscriptions.push(callback);\n  var removeSubscription = function removeSubscription() {\n    var idx = subscriptions.indexOf(callback);\n    if (idx > -1) {\n      subscriptions.splice(idx, 1);\n      onCleanup();\n    }\n  };\n  if (!detached && getCurrentScope()) {\n    onScopeDispose(removeSubscription);\n  }\n  return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  subscriptions.slice().forEach(function (callback) {\n    callback.apply(void 0, args);\n  });\n}\nvar fallbackRunWithContext = function fallbackRunWithContext(fn) {\n  return fn();\n};\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nvar ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nvar ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n  // Handle Map instances\n  if (target instanceof Map && patchToApply instanceof Map) {\n    patchToApply.forEach(function (value, key) {\n      return target.set(key, value);\n    });\n  } else if (target instanceof Set && patchToApply instanceof Set) {\n    // Handle Set instances\n    patchToApply.forEach(target.add, target);\n  }\n  // no need to go through symbols because they cannot be serialized anyway\n  for (var key in patchToApply) {\n    if (!patchToApply.hasOwnProperty(key)) continue;\n    var subPatch = patchToApply[key];\n    var targetValue = target[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && target.hasOwnProperty(key) && !isRef(subPatch) && !isReactive(subPatch)) {\n      // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n      // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n      // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n      target[key] = mergeReactiveObjects(targetValue, subPatch);\n    } else {\n      // @ts-expect-error: subPatch is a valid value\n      target[key] = subPatch;\n    }\n  }\n  return target;\n}\nvar skipHydrateSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia:skipHydration') : /* istanbul ignore next */Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n  return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n  return !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);\n}\nvar assign = Object.assign;\nfunction isComputed(o) {\n  return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n  var state = options.state,\n    actions = options.actions,\n    getters = options.getters;\n  var initialState = pinia.state.value[id];\n  var store;\n  function setup() {\n    if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n      /* istanbul ignore if */\n      if (isVue2) {\n        set(pinia.state.value, id, state ? state() : {});\n      } else {\n        pinia.state.value[id] = state ? state() : {};\n      }\n    }\n    // avoid creating a state in pinia.state.value\n    var localState = process.env.NODE_ENV !== 'production' && hot ?\n    // use ref() to unwrap refs inside state TODO: check if this is still necessary\n    toRefs(ref(state ? state() : {}).value) : toRefs(pinia.state.value[id]);\n    return assign(localState, actions, Object.keys(getters || {}).reduce(function (computedGetters, name) {\n      if (process.env.NODE_ENV !== 'production' && name in localState) {\n        console.warn(\"[\\uD83C\\uDF4D]: A getter cannot have the same name as another state property. Rename one of them. Found with \\\"\".concat(name, \"\\\" in store \\\"\").concat(id, \"\\\".\"));\n      }\n      computedGetters[name] = markRaw(computed(function () {\n        setActivePinia(pinia);\n        // it was created just before\n        var store = pinia._s.get(id);\n        // allow cross using stores\n        /* istanbul ignore if */\n        if (isVue2 && !store._r) return;\n        // @ts-expect-error\n        // return getters![name].call(context, context)\n        // TODO: avoid reading the getter while assigning with a global variable\n        return getters[name].call(store, store);\n      }));\n      return computedGetters;\n    }, {}));\n  }\n  store = createSetupStore(id, setup, options, pinia, hot, true);\n  return store;\n}\nfunction createSetupStore($id, setup) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var pinia = arguments.length > 3 ? arguments[3] : undefined;\n  var hot = arguments.length > 4 ? arguments[4] : undefined;\n  var isOptionsStore = arguments.length > 5 ? arguments[5] : undefined;\n  var scope;\n  var optionsForPlugin = assign({\n    actions: {}\n  }, options);\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && !pinia._e.active) {\n    throw new Error('Pinia destroyed');\n  }\n  // watcher options for $subscribe\n  var $subscribeOptions = {\n    deep: true\n  };\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production' && !isVue2) {\n    $subscribeOptions.onTrigger = function (event) {\n      /* istanbul ignore else */\n      if (isListening) {\n        debuggerEvents = event;\n        // avoid triggering this while the store is being built and the state is being set in pinia\n      } else if (isListening == false && !store._hotUpdating) {\n        // let patch send all the events together later\n        /* istanbul ignore else */\n        if (Array.isArray(debuggerEvents)) {\n          debuggerEvents.push(event);\n        } else {\n          console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n        }\n      }\n    };\n  }\n  // internal state\n  var isListening; // set to true at the end\n  var isSyncListening; // set to true at the end\n  var subscriptions = [];\n  var actionSubscriptions = [];\n  var debuggerEvents;\n  var initialState = pinia.state.value[$id];\n  // avoid setting the state for option stores if it is set\n  // by the setup\n  if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n    /* istanbul ignore if */\n    if (isVue2) {\n      set(pinia.state.value, $id, {});\n    } else {\n      pinia.state.value[$id] = {};\n    }\n  }\n  var hotState = ref({});\n  // avoid triggering too many listeners\n  // https://github.com/vuejs/pinia/issues/1129\n  var activeListener;\n  function $patch(partialStateOrMutator) {\n    var subscriptionMutation;\n    isListening = isSyncListening = false;\n    // reset the debugger events since patches are sync\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      debuggerEvents = [];\n    }\n    if (typeof partialStateOrMutator === 'function') {\n      partialStateOrMutator(pinia.state.value[$id]);\n      subscriptionMutation = {\n        type: MutationType.patchFunction,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    } else {\n      mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n      subscriptionMutation = {\n        type: MutationType.patchObject,\n        payload: partialStateOrMutator,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    }\n    var myListenerId = activeListener = Symbol();\n    nextTick().then(function () {\n      if (activeListener === myListenerId) {\n        isListening = true;\n      }\n    });\n    isSyncListening = true;\n    // because we paused the watcher, we need to manually call the subscriptions\n    triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n  }\n  var $reset = isOptionsStore ? function $reset() {\n    var state = options.state;\n    var newState = state ? state() : {};\n    // we use a patch to group all changes into one single subscription\n    this.$patch(function ($state) {\n      // @ts-expect-error: FIXME: shouldn't error?\n      assign($state, newState);\n    });\n  } : /* istanbul ignore next */\n  process.env.NODE_ENV !== 'production' ? function () {\n    throw new Error(\"\\uD83C\\uDF4D: Store \\\"\".concat($id, \"\\\" is built using the setup syntax and does not implement $reset().\"));\n  } : noop;\n  function $dispose() {\n    scope.stop();\n    subscriptions = [];\n    actionSubscriptions = [];\n    pinia._s.delete($id);\n  }\n  /**\n   * Helper that wraps function so it can be tracked with $onAction\n   * @param fn - action to wrap\n   * @param name - name of the action\n   */\n  var action = function action(fn) {\n    var name = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    if (ACTION_MARKER in fn) {\n      fn[ACTION_NAME] = name;\n      return fn;\n    }\n    var _wrappedAction = function wrappedAction() {\n      setActivePinia(pinia);\n      var args = Array.from(arguments);\n      var afterCallbackList = [];\n      var onErrorCallbackList = [];\n      function after(callback) {\n        afterCallbackList.push(callback);\n      }\n      function onError(callback) {\n        onErrorCallbackList.push(callback);\n      }\n      // @ts-expect-error\n      triggerSubscriptions(actionSubscriptions, {\n        args: args,\n        name: _wrappedAction[ACTION_NAME],\n        store: store,\n        after: after,\n        onError: onError\n      });\n      var ret;\n      try {\n        ret = fn.apply(this && this.$id === $id ? this : store, args);\n        // handle sync errors\n      } catch (error) {\n        triggerSubscriptions(onErrorCallbackList, error);\n        throw error;\n      }\n      if (ret instanceof Promise) {\n        return ret.then(function (value) {\n          triggerSubscriptions(afterCallbackList, value);\n          return value;\n        }).catch(function (error) {\n          triggerSubscriptions(onErrorCallbackList, error);\n          return Promise.reject(error);\n        });\n      }\n      // trigger after callbacks\n      triggerSubscriptions(afterCallbackList, ret);\n      return ret;\n    };\n    _wrappedAction[ACTION_MARKER] = true;\n    _wrappedAction[ACTION_NAME] = name; // will be set later\n    // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n    // because all the added properties are internals that are exposed through `$onAction()` only\n    return _wrappedAction;\n  };\n  var _hmrPayload = /*#__PURE__*/markRaw({\n    actions: {},\n    getters: {},\n    state: [],\n    hotState: hotState\n  });\n  var partialStore = {\n    _p: pinia,\n    // _s: scope,\n    $id: $id,\n    $onAction: addSubscription.bind(null, actionSubscriptions),\n    $patch: $patch,\n    $reset: $reset,\n    $subscribe: function $subscribe(callback) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var removeSubscription = addSubscription(subscriptions, callback, options.detached, function () {\n        return stopWatcher();\n      });\n      var stopWatcher = scope.run(function () {\n        return watch(function () {\n          return pinia.state.value[$id];\n        }, function (state) {\n          if (options.flush === 'sync' ? isSyncListening : isListening) {\n            callback({\n              storeId: $id,\n              type: MutationType.direct,\n              events: debuggerEvents\n            }, state);\n          }\n        }, assign({}, $subscribeOptions, options));\n      });\n      return removeSubscription;\n    },\n    $dispose: $dispose\n  };\n  /* istanbul ignore if */\n  if (isVue2) {\n    // start as non ready\n    partialStore._r = false;\n  }\n  var store = reactive(process.env.NODE_ENV !== 'production' || (process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT ? assign({\n    _hmrPayload: _hmrPayload,\n    _customProperties: markRaw(new Set()) // devtools custom properties\n  }, partialStore\n  // must be added later\n  // setupStore\n  ) : partialStore);\n  // store the partial store now so the setup of stores can instantiate each other before they are finished without\n  // creating infinite loops.\n  pinia._s.set($id, store);\n  var runWithContext = pinia._a && pinia._a.runWithContext || fallbackRunWithContext;\n  // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n  var setupStore = runWithContext(function () {\n    return pinia._e.run(function () {\n      return (scope = effectScope()).run(function () {\n        return setup({\n          action: action\n        });\n      });\n    });\n  });\n  // overwrite existing actions to support $onAction\n  for (var key in setupStore) {\n    var prop = setupStore[key];\n    if (isRef(prop) && !isComputed(prop) || isReactive(prop)) {\n      // mark it as a piece of state to be serialized\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        set(hotState.value, key, toRef(setupStore, key));\n        // createOptionStore directly sets the state in pinia.state.value so we\n        // can just skip that\n      } else if (!isOptionsStore) {\n        // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n        if (initialState && shouldHydrate(prop)) {\n          if (isRef(prop)) {\n            prop.value = initialState[key];\n          } else {\n            // probably a reactive object, lets recursively assign\n            // @ts-expect-error: prop is unknown\n            mergeReactiveObjects(prop, initialState[key]);\n          }\n        }\n        // transfer the ref to the pinia state to keep everything in sync\n        /* istanbul ignore if */\n        if (isVue2) {\n          set(pinia.state.value[$id], key, prop);\n        } else {\n          pinia.state.value[$id][key] = prop;\n        }\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.state.push(key);\n      }\n      // action\n    } else if (typeof prop === 'function') {\n      var actionValue = process.env.NODE_ENV !== 'production' && hot ? prop : action(prop, key);\n      // this a hot module replacement store because the hotUpdate method needs\n      // to do it with the right context\n      /* istanbul ignore if */\n      if (isVue2) {\n        set(setupStore, key, actionValue);\n      } else {\n        // @ts-expect-error\n        setupStore[key] = actionValue;\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.actions[key] = prop;\n      }\n      // list actions so they can be used in plugins\n      // @ts-expect-error\n      optionsForPlugin.actions[key] = prop;\n    } else if (process.env.NODE_ENV !== 'production') {\n      // add getters for devtools\n      if (isComputed(prop)) {\n        _hmrPayload.getters[key] = isOptionsStore ?\n        // @ts-expect-error\n        options.getters[key] : prop;\n        if (IS_CLIENT) {\n          var getters = setupStore._getters || (\n          // @ts-expect-error: same\n          setupStore._getters = markRaw([]));\n          getters.push(key);\n        }\n      }\n    }\n  }\n  // add the state, getters, and action properties\n  /* istanbul ignore if */\n  if (isVue2) {\n    Object.keys(setupStore).forEach(function (key) {\n      set(store, key, setupStore[key]);\n    });\n  } else {\n    assign(store, setupStore);\n    // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n    // Make `storeToRefs()` work with `reactive()` #799\n    assign(toRaw(store), setupStore);\n  }\n  // use this instead of a computed with setter to be able to create it anywhere\n  // without linking the computed lifespan to wherever the store is first\n  // created.\n  Object.defineProperty(store, '$state', {\n    get: function get() {\n      return process.env.NODE_ENV !== 'production' && hot ? hotState.value : pinia.state.value[$id];\n    },\n    set: function set(state) {\n      /* istanbul ignore if */\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        throw new Error('cannot set hotState');\n      }\n      $patch(function ($state) {\n        // @ts-expect-error: FIXME: shouldn't error?\n        assign($state, state);\n      });\n    }\n  });\n  // add the hotUpdate before plugins to allow them to override it\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    store._hotUpdate = markRaw(function (newStore) {\n      store._hotUpdating = true;\n      newStore._hmrPayload.state.forEach(function (stateKey) {\n        if (stateKey in store.$state) {\n          var newStateTarget = newStore.$state[stateKey];\n          var oldStateSource = store.$state[stateKey];\n          if (_typeof(newStateTarget) === 'object' && isPlainObject(newStateTarget) && isPlainObject(oldStateSource)) {\n            patchObject(newStateTarget, oldStateSource);\n          } else {\n            // transfer the ref\n            newStore.$state[stateKey] = oldStateSource;\n          }\n        }\n        // patch direct access properties to allow store.stateProperty to work as\n        // store.$state.stateProperty\n        set(store, stateKey, toRef(newStore.$state, stateKey));\n      });\n      // remove deleted state properties\n      Object.keys(store.$state).forEach(function (stateKey) {\n        if (!(stateKey in newStore.$state)) {\n          del(store, stateKey);\n        }\n      });\n      // avoid devtools logging this as a mutation\n      isListening = false;\n      isSyncListening = false;\n      pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n      isSyncListening = true;\n      nextTick().then(function () {\n        isListening = true;\n      });\n      for (var actionName in newStore._hmrPayload.actions) {\n        var actionFn = newStore[actionName];\n        set(store, actionName, action(actionFn, actionName));\n      }\n      // TODO: does this work in both setup and option store?\n      var _loop2 = function _loop2() {\n        var getter = newStore._hmrPayload.getters[getterName];\n        var getterValue = isOptionsStore ?\n        // special handling of options api\n        computed(function () {\n          setActivePinia(pinia);\n          return getter.call(store, store);\n        }) : getter;\n        set(store, getterName, getterValue);\n      };\n      for (var getterName in newStore._hmrPayload.getters) {\n        _loop2();\n      }\n      // remove deleted getters\n      Object.keys(store._hmrPayload.getters).forEach(function (key) {\n        if (!(key in newStore._hmrPayload.getters)) {\n          del(store, key);\n        }\n      });\n      // remove old actions\n      Object.keys(store._hmrPayload.actions).forEach(function (key) {\n        if (!(key in newStore._hmrPayload.actions)) {\n          del(store, key);\n        }\n      });\n      // update the values used in devtools and to allow deleting new properties later on\n      store._hmrPayload = newStore._hmrPayload;\n      store._getters = newStore._getters;\n      store._hotUpdating = false;\n    });\n  }\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n    var nonEnumerable = {\n      writable: true,\n      configurable: true,\n      // avoid warning on devtools trying to display this property\n      enumerable: false\n    };\n    ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach(function (p) {\n      Object.defineProperty(store, p, assign({\n        value: store[p]\n      }, nonEnumerable));\n    });\n  }\n  /* istanbul ignore if */\n  if (isVue2) {\n    // mark the store as ready before plugins\n    store._r = true;\n  }\n  // apply all plugins\n  pinia._p.forEach(function (extender) {\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n      var extensions = scope.run(function () {\n        return extender({\n          store: store,\n          app: pinia._a,\n          pinia: pinia,\n          options: optionsForPlugin\n        });\n      });\n      Object.keys(extensions || {}).forEach(function (key) {\n        return store._customProperties.add(key);\n      });\n      assign(store, extensions);\n    } else {\n      assign(store, scope.run(function () {\n        return extender({\n          store: store,\n          app: pinia._a,\n          pinia: pinia,\n          options: optionsForPlugin\n        });\n      }));\n    }\n  });\n  if (process.env.NODE_ENV !== 'production' && store.$state && _typeof(store.$state) === 'object' && typeof store.$state.constructor === 'function' && !store.$state.constructor.toString().includes('[native code]')) {\n    console.warn(\"[\\uD83C\\uDF4D]: The \\\"state\\\" must be a plain object. It cannot be\\n\" + \"\\tstate: () => new MyClass()\\n\" + \"Found in store \\\"\".concat(store.$id, \"\\\".\"));\n  }\n  // only apply hydrate to option stores with an initial state in pinia\n  if (initialState && isOptionsStore && options.hydrate) {\n    options.hydrate(store.$state, initialState);\n  }\n  isListening = true;\n  isSyncListening = true;\n  return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nidOrOptions, setup, setupOptions) {\n  var id;\n  var options;\n  var isSetupStore = typeof setup === 'function';\n  if (typeof idOrOptions === 'string') {\n    id = idOrOptions;\n    // the option store setup will contain the actual options in this case\n    options = isSetupStore ? setupOptions : setup;\n  } else {\n    options = idOrOptions;\n    id = idOrOptions.id;\n    if (process.env.NODE_ENV !== 'production' && typeof id !== 'string') {\n      throw new Error(\"[\\uD83C\\uDF4D]: \\\"defineStore()\\\" must be passed a store id as its first argument.\");\n    }\n  }\n  function useStore(pinia, hot) {\n    var hasContext = hasInjectionContext();\n    pinia =\n    // in test mode, ignore the argument provided as we can always retrieve a\n    // pinia instance with getActivePinia()\n    (process.env.NODE_ENV === 'test' && activePinia && activePinia._testing ? null : pinia) || (hasContext ? inject(piniaSymbol, null) : null);\n    if (pinia) setActivePinia(pinia);\n    if (process.env.NODE_ENV !== 'production' && !activePinia) {\n      throw new Error(\"[\\uD83C\\uDF4D]: \\\"getActivePinia()\\\" was called but there was no active Pinia. Are you trying to use a store before calling \\\"app.use(pinia)\\\"?\\n\" + \"See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n\" + \"This will fail in production.\");\n    }\n    pinia = activePinia;\n    if (!pinia._s.has(id)) {\n      // creating the store registers it in `pinia._s`\n      if (isSetupStore) {\n        createSetupStore(id, setup, options, pinia);\n      } else {\n        createOptionsStore(id, options, pinia);\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        // @ts-expect-error: not the right inferred type\n        useStore._pinia = pinia;\n      }\n    }\n    var store = pinia._s.get(id);\n    if (process.env.NODE_ENV !== 'production' && hot) {\n      var hotId = '__hot:' + id;\n      var newStore = isSetupStore ? createSetupStore(hotId, setup, options, pinia, true) : createOptionsStore(hotId, assign({}, options), pinia, true);\n      hot._hotUpdate(newStore);\n      // cleanup the state properties and the store from the cache\n      delete pinia.state.value[hotId];\n      pinia._s.delete(hotId);\n    }\n    if (process.env.NODE_ENV !== 'production' && IS_CLIENT) {\n      var currentInstance = getCurrentInstance();\n      // save stores in instances to access them devtools\n      if (currentInstance && currentInstance.proxy &&\n      // avoid adding stores that are just built for hot module replacement\n      !hot) {\n        var vm = currentInstance.proxy;\n        var cache = '_pStores' in vm ? vm._pStores : vm._pStores = {};\n        cache[id] = store;\n      }\n    }\n    // StoreGeneric cannot be casted towards Store\n    return store;\n  }\n  useStore.$id = id;\n  return useStore;\n}\nvar mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n  mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores() {\n  for (var _len2 = arguments.length, stores = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    stores[_key2] = arguments[_key2];\n  }\n  if (process.env.NODE_ENV !== 'production' && Array.isArray(stores[0])) {\n    console.warn(\"[\\uD83C\\uDF4D]: Directly pass all stores to \\\"mapStores()\\\" without putting them in an array:\\n\" + \"Replace\\n\" + \"\\tmapStores([useAuthStore, useCartStore])\\n\" + \"with\\n\" + \"\\tmapStores(useAuthStore, useCartStore)\\n\" + \"This will fail in production if not fixed.\");\n    stores = stores[0];\n  }\n  return stores.reduce(function (reduced, useStore) {\n    // @ts-expect-error: $id is added by defineStore\n    reduced[useStore.$id + mapStoreSuffix] = function () {\n      return useStore(this.$pinia);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce(function (reduced, key) {\n    reduced[key] = function () {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[key];\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce(function (reduced, key) {\n    // @ts-expect-error\n    reduced[key] = function () {\n      var store = useStore(this.$pinia);\n      var storeKey = keysOrMapper[key];\n      // for some reason TS is unable to infer the type of storeKey to be a\n      // function\n      return typeof storeKey === 'function' ? storeKey.call(this, store) :\n      // @ts-expect-error: FIXME: should work?\n      store[storeKey];\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nvar mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce(function (reduced, key) {\n    // @ts-expect-error\n    reduced[key] = function () {\n      var _useStore;\n      // @ts-expect-error: FIXME: should work?\n      return (_useStore = useStore(this.$pinia))[key].apply(_useStore, arguments);\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce(function (reduced, key) {\n    // @ts-expect-error\n    reduced[key] = function () {\n      var _useStore2;\n      // @ts-expect-error: FIXME: should work?\n      return (_useStore2 = useStore(this.$pinia))[keysOrMapper[key]].apply(_useStore2, arguments);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce(function (reduced, key) {\n    reduced[key] = {\n      get: function get() {\n        return useStore(this.$pinia)[key];\n      },\n      set: function set(value) {\n        return useStore(this.$pinia)[key] = value;\n      }\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce(function (reduced, key) {\n    reduced[key] = {\n      get: function get() {\n        return useStore(this.$pinia)[keysOrMapper[key]];\n      },\n      set: function set(value) {\n        return useStore(this.$pinia)[keysOrMapper[key]] = value;\n      }\n    };\n    return reduced;\n  }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n  // See https://github.com/vuejs/pinia/issues/852\n  // It's easier to just use toRefs() even if it includes more stuff\n  if (isVue2) {\n    // @ts-expect-error: toRefs include methods and others\n    return toRefs(store);\n  } else {\n    var rawStore = toRaw(store);\n    var refs = {};\n    var _loop3 = function _loop3(key) {\n      var value = rawStore[key];\n      // There is no native method to check for a computed\n      // https://github.com/vuejs/core/pull/4165\n      if (value.effect) {\n        // @ts-expect-error: too hard to type correctly\n        refs[key] =\n        // ...\n        computed({\n          get: function get() {\n            return store[key];\n          },\n          set: function set(value) {\n            store[key] = value;\n          }\n        });\n      } else if (isRef(value) || isReactive(value)) {\n        // @ts-expect-error: the key is state or getter\n        refs[key] =\n        // ---\n        toRef(store, key);\n      }\n    };\n    for (var key in rawStore) {\n      _loop3(key);\n    }\n    return refs;\n  }\n}\n\n/**\n * Vue 2 Plugin that must be installed for pinia to work. Note **you don't need\n * this plugin if you are using Nuxt.js**. Use the `buildModule` instead:\n * https://pinia.vuejs.org/ssr/nuxt.html.\n *\n * @example\n * ```js\n * import Vue from 'vue'\n * import { PiniaVuePlugin, createPinia } from 'pinia'\n *\n * Vue.use(PiniaVuePlugin)\n * const pinia = createPinia()\n *\n * new Vue({\n *   el: '#app',\n *   // ...\n *   pinia,\n * })\n * ```\n *\n * @param _Vue - `Vue` imported from 'vue'.\n */\nvar PiniaVuePlugin = function PiniaVuePlugin(_Vue) {\n  // Equivalent of\n  // app.config.globalProperties.$pinia = pinia\n  _Vue.mixin({\n    beforeCreate: function beforeCreate() {\n      var options = this.$options;\n      if (options.pinia) {\n        var pinia = options.pinia;\n        // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/main/src/apis/inject.ts#L31\n        /* istanbul ignore else */\n        if (!this._provided) {\n          var provideCache = {};\n          Object.defineProperty(this, '_provided', {\n            get: function get() {\n              return provideCache;\n            },\n            set: function set(v) {\n              return Object.assign(provideCache, v);\n            }\n          });\n        }\n        this._provided[piniaSymbol] = pinia;\n        // propagate the pinia instance in an SSR friendly way\n        // avoid adding it to nuxt twice\n        /* istanbul ignore else */\n        if (!this.$pinia) {\n          this.$pinia = pinia;\n        }\n        pinia._a = this;\n        if (IS_CLIENT) {\n          // this allows calling useStore() outside of a component setup after\n          // installing pinia's plugin\n          setActivePinia(pinia);\n        }\n        if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n          registerPiniaDevtools(pinia._a, pinia);\n        }\n      } else if (!this.$pinia && options.parent && options.parent.$pinia) {\n        this.$pinia = options.parent.$pinia;\n      }\n    },\n    destroyed: function destroyed() {\n      delete this._pStores;\n    }\n  });\n};\nexport { MutationType, PiniaVuePlugin, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
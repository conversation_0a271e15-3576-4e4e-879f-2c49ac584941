{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/*\r\n/ 全局静态变量定义\r\n切勿随意修改数组次序，很多地方已下标方式使用\r\n */\n\nexport var page = {\n  limit: [20, 40, 60, 80, 100],\n  page: 1,\n  layout: 'total, sizes, prev, pager, next, jumper'\n};\n\n/**\r\n * 无限极分类type\r\n * @type {{product: number, attachment: number, menu: number, article: number, operator: number}}\r\n */\nexport var categoryType = [\n// 1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置\n{\n  name: '产品分类',\n  value: 1,\n  shortName: '产品'\n}, {\n  name: '附件分类',\n  value: 2,\n  shortName: '附件'\n}, {\n  name: '文章分类',\n  value: 3,\n  shortName: '文章'\n}, {\n  name: '设置分类',\n  value: 4,\n  shortName: '设置'\n}, {\n  name: '菜单分类',\n  value: 5,\n  shortName: '菜单'\n}, {\n  name: '配置分类',\n  value: 6,\n  shortName: '配置'\n}, {\n  name: '秒杀配置',\n  value: 7,\n  shortName: '秒杀'\n}];\nexport var roleListStatus = [{\n  label: '全部',\n  value: ''\n}, {\n  label: '显示',\n  value: 1\n}, {\n  label: '不显示',\n  value: 0\n}];\nexport var showHiddenStatus = [{\n  label: '显示',\n  value: '‘1’'\n}, {\n  label: '不显示',\n  value: '‘0’'\n}];\nexport var switchStatus = [{\n  label: '开启',\n  value: 1\n}, {\n  label: '关闭',\n  value: 0\n}];\nexport var deletedOrNormal = [{\n  label: '正常',\n  value: 0\n}, {\n  label: '已删除',\n  value: 1\n}];\n\n/**\r\n * 暂时弃用\r\n * @type {*[]}\r\n */\nexport var configCategory = [{\n  label: '系统',\n  value: '0'\n}, {\n  label: '应用',\n  value: '1'\n}, {\n  label: '支付',\n  value: '2'\n}, {\n  label: '其他',\n  value: '3'\n}];\n\n/**\r\n * 表单配置集合集中配置\r\n * @type {{id: number, dis: string}[]}\r\n */\nexport var formConfigIds = [{\n  id: 84,\n  dis: '微信公众号表单配置'\n}, {\n  id: 86,\n  dis: '秒杀配置'\n}];\n\n/**\r\n * 时间选择器\r\n */\nexport var fromList = {\n  title: '选择时间',\n  custom: true,\n  fromTxt: [{\n    text: '全部',\n    val: ''\n  }, {\n    text: '今天',\n    val: 'today'\n  }, {\n    text: '昨天',\n    val: 'yesterday'\n  }, {\n    text: '最近7天',\n    val: 'lately7'\n  }, {\n    text: '最近30天',\n    val: 'lately30'\n  }, {\n    text: '本月',\n    val: 'month'\n  }, {\n    text: '本年',\n    val: 'year'\n  }]\n};\n\n// 统计管理时间选择器\nexport var timeList = {\n  title: '选择时间',\n  custom: true,\n  fromTxt: [{\n    text: '昨天',\n    val: \"\"\n  }, {\n    text: '最近7天',\n    val: 'lately7'\n  }, {\n    text: '最近30天',\n    val: 'lately30'\n  }]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "export default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    iconName() {\n      return `#icon-${this.iconClass}`;\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className;\n      } else {\n        return 'svg-icon';\n      }\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport * as constants from '@/utils/constants.js';\nimport { formatDates } from '@/utils/index';\n\n// 公共过滤器\nexport function filterEmpty(val) {\n  let _result = '-';\n  if (!val) {\n    return _result;\n  }\n  _result = val;\n  return _result;\n}\n\n// 时间过滤器\nexport function formatDate(time) {\n  if (time !== 0) {\n    const date = new Date(time * 1000);\n    return formatDates(date, 'yyyy-MM-dd hh:mm');\n  }\n}\nexport function filterYesOrNo(value) {\n  return value ? '是' : '否';\n}\nexport function filterShowOrHide(value) {\n  return value ? '显示' : '不显示';\n}\nexport function filterShowOrHideForFormConfig(value) {\n  return value === '‘0’' ? '显示' : '不显示';\n}\nexport function filterYesOrNoIs(value) {\n  return value ? '否' : '是';\n}\nexport function filterCategroyType(value) {\n  return constants.categoryType.filter(item => value === item.value)[0].name;\n}\nexport function filterConfigCategory(value) {\n  return constants.configCategory.filter(item => value === item.value)[0].label;\n}\n\n/**\r\n * @description 公众号回复类型\r\n */\nexport function keywordStatusFilter(status) {\n  const statusMap = {\n    text: '文字消息',\n    image: '图片消息',\n    news: '图文消息',\n    voice: '声音消息'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 优惠券类型\r\n */\nexport function couponUserTypeFilter(status) {\n  const statusMap = {\n    1: '通用券',\n    2: '商品券',\n    3: '品类券'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 优惠券领取方式\r\n */\nexport function couponTypeFilter(status) {\n  const statusMap = {\n    1: '手动领取',\n    2: '新人券',\n    3: '赠送券'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 文章分类\r\n */\nexport function articleTypeFilter(status) {\n  if (!status) {\n    return '';\n  }\n  let arrayList = JSON.parse(localStorage.getItem('adminArticleClassify'));\n  if (arrayList.filter(item => Number(status) === Number(item.id)).length < 1) {\n    return '';\n  }\n  return arrayList.filter(item => Number(status) === Number(item.id))[0].name;\n}\n\n/**\r\n * @description 支付状态\r\n */\nexport function payStatusFilter(status) {\n  const statusMap = {\n    false: '未支付',\n    true: '已支付'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 提现方式\r\n */\nexport function extractTypeFilter(status) {\n  const statusMap = {\n    bank: '银行卡',\n    alipay: '支付宝',\n    weixin: '微信'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 充值类型\r\n */\nexport function rechargeTypeFilter(status) {\n  const statusMap = {\n    public: '微信公众号',\n    weixinh5: '微信H5支付',\n    routine: '小程序'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 财务审核状态\r\n */\nexport function extractStatusFilter(status) {\n  const statusMap = {\n    '-1': '已拒绝',\n    0: '审核中',\n    1: '已提现'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 砍价状态\r\n */\nexport function bargainStatusFilter(status) {\n  const statusMap = {\n    1: '进行中',\n    2: '未完成',\n    3: '已成功'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 砍价状态\r\n */\nexport function bargainColorFilter(status) {\n  const statusMap = {\n    1: '',\n    2: 'danger',\n    3: 'success'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 拼团状态\r\n */\nexport function groupStatusFilter(status) {\n  const statusMap = {\n    1: '进行中',\n    2: '已成功',\n    3: '未完成'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 拼团状态\r\n */\nexport function groupColorFilter(status) {\n  const statusMap = {\n    1: '',\n    2: 'success',\n    3: 'danger'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 一号通tab值\r\n */\nexport function onePassTypeFilter(status) {\n  const statusMap = {\n    sms: '短信',\n    copy: '商品采集',\n    expr_query: '物流查询',\n    expr_dump: '电子面单打印'\n  };\n  return statusMap[status];\n}\n\n/**\r\n * @description 氛围图、活动边框使用范围类型\r\n */\nexport function activityMethodFilter(status) {\n  const statusMap = {\n    0: '全部商品',\n    1: '指定商品',\n    2: '指定品牌',\n    3: '指定商品分类',\n    4: '指定商户'\n  };\n  return statusMap[status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
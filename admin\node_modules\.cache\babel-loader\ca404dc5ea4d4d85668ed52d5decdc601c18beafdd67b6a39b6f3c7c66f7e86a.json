{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\n\n/**\r\n * 提货点分页列表\r\n * @param pram\r\n */\nexport function storeListApi(data) {\n  return request({\n    url: '/admin/system/store/list',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 提货点数量\r\n * @param pram\r\n */\nexport function storeGetCountApi(params) {\n  return request({\n    url: '/admin/system/store/getCount',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 提货点门店状态\r\n * @param pram\r\n */\nexport function storeUpdateStatusApi(data) {\n  return request({\n    url: '/admin/system/store/update/status',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 提货点门店刪除\r\n * @param pram\r\n */\nexport function storeDeleteApi(data) {\n  return request({\n    url: '/admin/system/store/delete',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 提货点门店回收站刪除\r\n * @param pram\r\n */\nexport function allDeleteApi(params) {\n  return request({\n    url: '/admin/system/store/completely/delete',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 提货点添加\r\n * @param pram\r\n */\nexport function storeSaveApi(data) {\n  return request({\n    url: '/admin/system/store/save',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 提货点详情\r\n * @param pram\r\n */\nexport function storeInfoApi(data) {\n  return request({\n    url: '/admin/system/store/info',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 提货点修改\r\n * @param pram\r\n */\nexport function storeUpdateApi(data, id) {\n  // const param = ;\n  return request({\n    url: '/admin/system/store/update',\n    method: 'post',\n    params: {\n      id: id\n    },\n    data: data\n  });\n}\n\n/**\r\n * 提货点恢复\r\n * @param pram\r\n */\nexport function storeRecoveryApi(params) {\n  return request({\n    url: '/admin/system/store/recovery',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 核销员分页列表\r\n * @param pram\r\n */\nexport function storeStaffListApi(data) {\n  return request({\n    url: '/admin/system/store/staff/list',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 核销员添加\r\n * @param pram\r\n */\nexport function storeStaffSaveApi(data) {\n  return request({\n    url: '/admin/system/store/staff/save',\n    method: 'POST',\n    params: data\n  });\n}\n\n/**\r\n * 核销员删除\r\n * @param pram\r\n */\nexport function storeStaffDeleteApi(data) {\n  return request({\n    url: '/admin/system/store/staff/delete',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 核销员编辑\r\n * @param pram\r\n */\nexport function storeStaffUpdateApi(data) {\n  return request({\n    url: '/admin/system/store/staff/update',\n    method: 'POST',\n    params: data\n  });\n}\n\n/**\r\n * 核销员编辑\r\n * @param pram\r\n */\nexport function storeStaffInfoApi(id) {\n  return request({\n    url: '/admin/system/store/staff/info',\n    method: 'get',\n    params: id\n  });\n}\n\n/**\r\n * 核销员编辑\r\n * @param pram\r\n */\nexport function storeStaffUpdateStatusApi(data) {\n  return request({\n    url: '/admin/system/store/staff/update/status',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 核销员编辑\r\n * @param pram\r\n */\nexport function userListApi(data) {\n  return request({\n    url: '/admin/wechat/user/list',\n    method: 'get',\n    params: data\n  });\n}\n\n/**\r\n * 核销订单\r\n * @param pram\r\n */\nexport function orderListApi(params) {\n  return request({\n    url: '/admin/system/store/order/list',\n    method: 'post',\n    params: params\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
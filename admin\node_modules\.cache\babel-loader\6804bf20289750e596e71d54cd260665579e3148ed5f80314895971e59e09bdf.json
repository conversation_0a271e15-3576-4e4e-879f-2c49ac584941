{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n//小程序 微信过滤器\nimport Cookies from 'js-cookie';\n/**\r\n * @description 小程序所属类目\r\n */\nexport function wxCategoryFilter(status) {\n  if (!status) {\n    return '';\n  }\n  if (!Cookies.get('WxCategory')) {\n    return;\n  }\n  var arrayList = JSON.parse(Cookies.get('WxCategory'));\n  if (arrayList.filter(function (item) {\n    return Number(status) === Number(item.id);\n  }).length < 1) {\n    return '';\n  }\n  return arrayList.filter(function (item) {\n    return Number(status) === Number(item.id);\n  })[0].name;\n}\n\n/**\r\n * @description 小程序模板类型\r\n */\nexport function wxTypeFilter(status) {\n  var statusMap = {\n    2: '一次性订阅',\n    3: '长期订阅'\n  };\n  return statusMap[status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"timeselect-component\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_time_picker = _resolveComponent(\"el-time-picker\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_time_picker, {\n    \"model-value\": $props.modelValue,\n    disabled: $props.disabled,\n    placeholder: $props.placeholder,\n    format: \"HH:mm:ss\",\n    \"value-format\": \"HH:mm:ss\",\n    \"onUpdate:modelValue\": $options.handleChange\n  }, null, 8, [\"model-value\", \"disabled\", \"placeholder\", \"onUpdate:modelValue\"])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport WechatJSSDK from 'wechat-jssdk/dist/client.umd';\nimport { getWechatConfig, wechatAuth } from '@/api/wxApi';\nimport { getToken, removeToken, setToken } from '@/utils/auth';\nimport { parseQuery } from '@/utils';\nimport Cookies from 'js-cookie';\nconst STATE_KEY = 'wx_authorize_state';\nimport store from '@/store';\nconst WX_AUTH = 'wx_auth';\nconst BACK_URL = 'login_back_url';\nconst LOGINTYPE = 'loginType';\nlet instance;\nlet wechatObj;\nconst LONGITUDE = 'user_longitude';\nconst LATITUDE = 'user_latitude';\nconst WECHAT_SCRIPT_URL = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';\n\n/**\r\n * 是否是微信\r\n */\nexport function isWeixin() {\n  return navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1;\n}\n\n/**\r\n * 是否是移动端\r\n */\nexport function isPhone() {\n  return /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent);\n}\nexport default function wechat() {\n  return new Promise((resolve, reject) => {\n    if (instance) return resolve(instance);\n    getWechatConfig().then(res => {\n      const _wx = WechatJSSDK(res);\n      wechatObj = _wx;\n      _wx.initialize().then(() => {\n        instance = _wx.wx;\n        instance.initConfig = res;\n        resolve(instance);\n      }).catch(reject);\n    }).catch(err => {\n      reject(err);\n    });\n  });\n}\nexport function loginByWxCode(code) {\n  return new Promise((resolve, reject) => {\n    let loginType = getToken();\n    wechatAuth(code).then(res => {\n      store.commit('SET_TOKEN', res.token);\n      setToken(res.token);\n      Cookies.set(WX_AUTH, code);\n      resolve(res);\n    }).catch(err => {\n      reject(err);\n    });\n  });\n}\nexport function getWXCodeByUrl(path, step) {\n  if (getToken()) return;\n  generatorWxUrl(path, step);\n}\nexport function generatorWxUrl(path, step) {\n  wechat().then(wx => {\n    window.location.href = getAuthUrl(wx.initConfig, path, step);\n  }).catch(err => {\n    reject(err);\n  });\n}\nfunction getAuthUrl(config, path, step) {\n  const finalUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.appId}&redirect_uri=${encodeURIComponent(path)}&response_type=code&scope=snsapi_base&state=${step}#wechat_redirect`;\n  return finalUrl;\n}\nfunction getQueryString(name) {\n  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');\n  var reg_rewrite = new RegExp('(^|/)' + name + '/([^/]*)(/|$)', 'i');\n  var r = window.location.search.substr(1).match(reg);\n  var q = window.location.pathname.substr(1).match(reg_rewrite);\n  if (r != null) {\n    return unescape(r[2]);\n  } else if (q != null) {\n    return unescape(q[2]);\n  } else {\n    return null;\n  }\n}\n\n/**\r\n * 公众号事件\r\n * @param name 事件名\r\n * @param config 配置\r\n * @returns {Promise<unknown>}\r\n */\nexport function wechatEvevt(name, config) {\n  return new Promise((resolve, reject) => {\n    let wx;\n    let configDefault = {\n      fail(res) {\n        if (wx) return reject({\n          is_ready: true,\n          wx: wx\n        });\n        getWechatConfig().then(res => {\n          wechatObj.signSignature({\n            nonceStr: res.nonceStr,\n            signature: res.signature,\n            timestamp: res.timestamp\n          });\n          wx = wechatObj.getOriginalWx();\n          reject({\n            is_ready: true,\n            wx: wx\n          });\n        });\n      },\n      success(res) {\n        resolve(res);\n      },\n      cancel(err) {\n        reject(err);\n      },\n      complete(err) {\n        reject(err);\n      }\n    };\n    Object.assign(configDefault, config);\n    getWechatConfig().then(res => {\n      const _wx = WechatJSSDK(res);\n      _wx.initialize().then(() => {\n        instance = _wx.getOriginalWx();\n        instance.ready(() => {\n          if (typeof name === 'object') {\n            name.forEach(item => {\n              instance[item] && instance[item](configDefault);\n            });\n          } else instance[name] && instance[name](configDefault);\n        });\n      });\n    });\n  });\n}\nexport function ready() {\n  return new Promise(resolve => {\n    if (typeof instance !== 'undefined') {\n      instance.ready(() => {\n        resolve(instance);\n      });\n    } else {\n      getWechatConfig().then(res => {\n        const _wx = WechatJSSDK(res);\n        _wx.initialize().then(() => {\n          instance = _wx.wx;\n          instance.ready(() => {\n            resolve(instance);\n          });\n        });\n      });\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
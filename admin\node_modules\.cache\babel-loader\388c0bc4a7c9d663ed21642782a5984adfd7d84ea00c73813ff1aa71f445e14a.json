{"ast": null, "code": "import { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus';\nvar loadingInstance;\nexport default {\n  // 消息提示\n  msg: function msg(content) {\n    ElMessage.info(content);\n  },\n  // 错误消息\n  msgError: function msgError(content) {\n    ElMessage.error(content);\n  },\n  // 成功消息\n  msgSuccess: function msgSuccess(content) {\n    ElMessage.success(content);\n  },\n  // 警告消息\n  msgWarning: function msgWarning(content) {\n    ElMessage.warning(content);\n  },\n  // 弹出提示\n  alert: function alert(content) {\n    ElMessageBox.alert(content, '系统提示');\n  },\n  // 错误提示\n  alertError: function alertError(content) {\n    ElMessageBox.alert(content, '系统提示', {\n      type: 'error'\n    });\n  },\n  // 成功提示\n  alertSuccess: function alertSuccess(content) {\n    ElMessageBox.alert(content, '系统提示', {\n      type: 'success'\n    });\n  },\n  // 警告提示\n  alertWarning: function alertWarning(content) {\n    ElMessageBox.alert(content, '系统提示', {\n      type: 'warning'\n    });\n  },\n  // 通知提示\n  notify: function notify(content) {\n    ElNotification.info(content);\n  },\n  // 错误通知\n  notifyError: function notifyError(content) {\n    ElNotification.error(content);\n  },\n  // 成功通知\n  notifySuccess: function notifySuccess(content) {\n    ElNotification.success(content);\n  },\n  // 警告通知\n  notifyWarning: function notifyWarning(content) {\n    ElNotification.warning(content);\n  },\n  // 确认窗体\n  confirm: function confirm(content) {\n    return ElMessageBox.confirm(content, '系统提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n      customClass: 'deleteConfirm'\n    });\n  },\n  // 打开遮罩层\n  loading: function loading(content) {\n    loadingInstance = ElLoading.service({\n      lock: true,\n      text: content,\n      spinner: 'el-icon-loading',\n      background: 'rgba(0, 0, 0, 0.7)'\n    });\n  },\n  // 关闭遮罩层\n  closeLoading: function closeLoading() {\n    loadingInstance.close();\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nconst userRouter = {\n  path: '/user',\n  component: Layout,\n  redirect: '/user/index',\n  name: 'User',\n  meta: {\n    title: '用户',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'index',\n    component: () => import('@/views/user/list/index'),\n    name: 'UserIndex',\n    meta: {\n      title: '用户管理',\n      icon: ''\n    }\n  }, {\n    path: 'grade',\n    component: () => import('@/views/user/grade/index'),\n    name: 'Grade',\n    meta: {\n      title: '用户等级',\n      icon: ''\n    }\n  }, {\n    path: 'label',\n    component: () => import('@/views/user/group/index'),\n    name: 'Label',\n    meta: {\n      title: '用户标签',\n      icon: ''\n    }\n  }, {\n    path: 'group',\n    component: () => import('@/views/user/group/index'),\n    name: 'Group',\n    meta: {\n      title: '用户分组',\n      icon: ''\n    }\n  }]\n};\nexport default userRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
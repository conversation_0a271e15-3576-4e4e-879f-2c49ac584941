{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from '@/store';\nexport default {\n  inserted: function inserted(el, binding, vnode) {\n    var value = binding.value;\n    var super_admin = 'admin';\n    var roles = store.state.user.name;\n    if (value && value instanceof Array && value.length > 0) {\n      var roleFlag = value;\n      var hasRole = roles.some(function (role) {\n        return super_admin === role || roleFlag.includes(role);\n      });\n      if (!hasRole) {\n        el.parentNode && el.parentNode.removeChild(el);\n      }\n    } else {\n      throw new Error(\"\\u8BF7\\u8BBE\\u7F6E\\u89D2\\u8272\\u6743\\u9650\\u6807\\u7B7E\\u503C\\\"\");\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
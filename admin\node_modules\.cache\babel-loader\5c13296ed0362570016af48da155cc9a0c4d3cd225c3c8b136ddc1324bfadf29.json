{"ast": null, "code": "import _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _objectSpread from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport router from './router';\nimport store from './store';\nimport { ElMessage } from 'element-plus';\nimport NProgress from 'nprogress'; // progress bar\nimport 'nprogress/nprogress.css'; // progress bar style\nimport { getToken } from '@/utils/auth'; // get token from cookie\nimport getPageTitle from '@/utils/get-page-title';\nNProgress.configure({\n  showSpinner: false\n}); // NProgress Configuration\n\nvar whiteList = ['/login', '/auth-redirect']; // no redirect whitelist\n\nrouter.beforeEach(/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(to, from, next) {\n    var hasToken, hasRoles, roles, accessRoutes, _t;\n    return _regenerator().w(function (_context) {\n      while (1) switch (_context.p = _context.n) {\n        case 0:\n          // start progress bar\n          NProgress.start();\n\n          // set page title\n          document.title = getPageTitle(to.meta.title);\n\n          // determine whether the user has logged in\n          hasToken = getToken();\n          if (!hasToken) {\n            _context.n = 8;\n            break;\n          }\n          if (!(to.path === '/login')) {\n            _context.n = 1;\n            break;\n          }\n          // if is logged in, redirect to the home page\n          next({\n            path: '/'\n          });\n          NProgress.done();\n          _context.n = 7;\n          break;\n        case 1:\n          hasRoles = store.getters.roles && store.getters.roles.length > 0;\n          if (!hasRoles) {\n            _context.n = 2;\n            break;\n          }\n          next();\n          _context.n = 7;\n          break;\n        case 2:\n          _context.p = 2;\n          _context.n = 3;\n          return store.dispatch('user/getInfo');\n        case 3:\n          roles = _context.v;\n          _context.n = 4;\n          return store.dispatch('permission/generateRoutes', roles);\n        case 4:\n          accessRoutes = _context.v;\n          router.addRoutes(accessRoutes);\n          next(_objectSpread(_objectSpread({}, to), {}, {\n            replace: true\n          }));\n          _context.n = 7;\n          break;\n        case 5:\n          _context.p = 5;\n          _t = _context.v;\n          _context.n = 6;\n          return store.dispatch('user/resetToken');\n        case 6:\n          ElMessage.error(_t || 'Has Error');\n          next(\"/login?redirect=\".concat(to.path));\n          NProgress.done();\n        case 7:\n          _context.n = 9;\n          break;\n        case 8:\n          /* has no token*/\n          if (whiteList.indexOf(to.path) !== -1) {\n            // in the free login whitelist, go directly\n            next();\n          } else {\n            // other pages that do not have permission to access are redirected to the login page.\n            next(\"/login?redirect=\".concat(to.path));\n            NProgress.done();\n          }\n        case 9:\n          return _context.a(2);\n      }\n    }, _callee, null, [[2, 5]]);\n  }));\n  return function (_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}());\nrouter.afterEach(function () {\n  // finish progress bar\n  NProgress.done();\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
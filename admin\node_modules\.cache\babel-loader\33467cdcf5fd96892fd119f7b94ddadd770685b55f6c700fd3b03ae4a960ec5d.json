{"ast": null, "code": "import _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia';\nimport { asyncRoutes, constantRoutes } from '@/router';\nimport * as roleApi from '@/api/roleApi.js';\nimport * as Auth from '@/libs/wechat';\nimport { formatRoutes } from '@/utils/parsing';\n\n/**\n * Filter asynchronous routing tables by recursion\n * @param routes asyncRoutes\n * @param roles\n */\nexport function filterAsyncRoutes(routes, roles) {\n  var res = [];\n  routes.forEach(function (route) {\n    var tmp = _objectSpread({}, route);\n    if (tmp.child) {\n      tmp.child = filterAsyncRoutes(tmp.child, roles);\n    }\n    res.push(tmp);\n  });\n  return res;\n}\nfunction comRouter(menus, asyncRouter, hasLeft) {\n  var router = [];\n  asyncRouter.forEach(function (item) {\n    if (menus.indexOf(item.name) !== -1 || hasLeft.indexOf(item.name) !== -1) {\n      if (item.children && item.children.length) {\n        item.children = comRouter(menus, item.children, hasLeft);\n      }\n      router.push(item);\n    }\n  });\n  return router;\n}\nexport var usePermissionStore = defineStore('permission', {\n  state: function state() {\n    return {\n      routes: [],\n      addRoutes: [],\n      topbarRouters: [],\n      sidebarRouters: []\n    };\n  },\n  getters: {\n    permissionRoutes: function permissionRoutes(state) {\n      return state.routes;\n    },\n    sidebarRoutes: function sidebarRoutes(state) {\n      return state.sidebarRouters;\n    },\n    topbarRoutes: function topbarRoutes(state) {\n      return state.topbarRouters;\n    }\n  },\n  actions: {\n    // 生成路由\n    generateRoutes: function generateRoutes(roleid) {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              return _context2.a(2, new Promise(/*#__PURE__*/function () {\n                var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(resolve) {\n                  var accessedRoutes, menus, hasLeft, res, _t;\n                  return _regenerator().w(function (_context) {\n                    while (1) switch (_context.p = _context.n) {\n                      case 0:\n                        accessedRoutes = [];\n                        menus = [];\n                        hasLeft = [];\n                        _context.p = 1;\n                        _context.n = 2;\n                        return roleApi.getMenus();\n                      case 2:\n                        res = _context.v;\n                        if (res.data) {\n                          menus = formatRoutes(res.data);\n                          hasLeft = res.data.map(function (item) {\n                            return item.menu_name;\n                          });\n                        }\n                        _context.n = 4;\n                        break;\n                      case 3:\n                        _context.p = 3;\n                        _t = _context.v;\n                        console.error('获取菜单失败:', _t);\n                      case 4:\n                        accessedRoutes = comRouter(menus, constantRoutes, hasLeft);\n                        _this.addRoutes = accessedRoutes;\n                        _this.routes = accessedRoutes;\n                        _this.sidebarRouters = accessedRoutes;\n                        _this.topbarRouters = accessedRoutes;\n                        resolve(accessedRoutes);\n                      case 5:\n                        return _context.a(2);\n                    }\n                  }, _callee, null, [[1, 3]]);\n                }));\n                return function (_x) {\n                  return _ref.apply(this, arguments);\n                };\n              }()));\n          }\n        }, _callee2);\n      }))();\n    },\n    // 设置路由\n    setRoutes: function setRoutes(routes) {\n      this.addRoutes = routes;\n      this.routes = routes;\n    },\n    // 设置顶部路由\n    setTopbarRoutes: function setTopbarRoutes(routes) {\n      this.topbarRouters = routes;\n    },\n    // 设置侧边栏路由\n    setSidebarRouters: function setSidebarRouters(routes) {\n      this.sidebarRouters = routes;\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
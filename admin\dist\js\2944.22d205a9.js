"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[2944],{2944:function(s,a,t){t.r(a),t.d(a,{default:function(){return p}});var l=t(641),e=t(33);const d={class:"dashboard-container"},c={class:"stats-content"},n={class:"stats-number"},r={class:"stats-content"},u={class:"stats-number"},k={class:"stats-content"},o={class:"stats-number"},b={class:"stats-content"},v={class:"stats-number"};function i(s,a,t,i,_,L){const f=(0,l.g2)("el-card"),p=(0,l.g2)("el-col"),h=(0,l.g2)("el-row");return(0,l.uX)(),(0,l.<PERSON>)("div",d,[a[6]||(a[6]=(0,l.Lk)("div",{class:"dashboard-header"},[(0,l.Lk)("h1",null,"CRMEB管理后台"),(0,l.Lk)("p",null,"欢迎使用CRMEB电商管理系统")],-1)),(0,l.bF)(h,{gutter:20,class:"stats-row"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{span:6},{default:(0,l.k6)(()=>[(0,l.bF)(f,{class:"stats-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",c,[(0,l.Lk)("div",n,(0,e.v_)(_.stats.users),1),a[0]||(a[0]=(0,l.Lk)("div",{class:"stats-label"},"用户总数",-1))])]),_:1})]),_:1}),(0,l.bF)(p,{span:6},{default:(0,l.k6)(()=>[(0,l.bF)(f,{class:"stats-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",r,[(0,l.Lk)("div",u,(0,e.v_)(_.stats.orders),1),a[1]||(a[1]=(0,l.Lk)("div",{class:"stats-label"},"订单总数",-1))])]),_:1})]),_:1}),(0,l.bF)(p,{span:6},{default:(0,l.k6)(()=>[(0,l.bF)(f,{class:"stats-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",k,[(0,l.Lk)("div",o,(0,e.v_)(_.stats.products),1),a[2]||(a[2]=(0,l.Lk)("div",{class:"stats-label"},"商品总数",-1))])]),_:1})]),_:1}),(0,l.bF)(p,{span:6},{default:(0,l.k6)(()=>[(0,l.bF)(f,{class:"stats-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",b,[(0,l.Lk)("div",v,"¥"+(0,e.v_)(_.stats.revenue),1),a[3]||(a[3]=(0,l.Lk)("div",{class:"stats-label"},"总收入",-1))])]),_:1})]),_:1})]),_:1}),(0,l.bF)(h,{gutter:20,class:"charts-row"},{default:(0,l.k6)(()=>[(0,l.bF)(p,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(f,null,{default:(0,l.k6)(()=>a[4]||(a[4]=[(0,l.Lk)("div",{slot:"header"},[(0,l.Lk)("span",null,"销售趋势")],-1),(0,l.Lk)("div",{class:"chart-placeholder"},[(0,l.Lk)("p",null,"销售趋势图表")],-1)])),_:1,__:[4]})]),_:1}),(0,l.bF)(p,{span:12},{default:(0,l.k6)(()=>[(0,l.bF)(f,null,{default:(0,l.k6)(()=>a[5]||(a[5]=[(0,l.Lk)("div",{slot:"header"},[(0,l.Lk)("span",null,"用户增长")],-1),(0,l.Lk)("div",{class:"chart-placeholder"},[(0,l.Lk)("p",null,"用户增长图表")],-1)])),_:1,__:[5]})]),_:1})]),_:1})])}var _={name:"Dashboard",data(){return{stats:{users:1234,orders:567,products:89,revenue:12345.67}}},mounted(){this.loadStats()},methods:{loadStats(){console.log("Loading dashboard stats...")}}},L=t(6262);const f=(0,L.A)(_,[["render",i],["__scopeId","data-v-4ce53600"]]);var p=f}}]);
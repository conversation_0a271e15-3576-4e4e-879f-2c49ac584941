{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      loginForm: {\n        username: 'admin',\n        password: '123456'\n      },\n      loginRules: {\n        username: [{\n          required: true,\n          trigger: 'blur',\n          message: '请输入用户名'\n        }],\n        password: [{\n          required: true,\n          trigger: 'blur',\n          message: '请输入密码'\n        }]\n      },\n      loading: false,\n      passwordType: 'password',\n      redirect: undefined\n    };\n  },\n  methods: {\n    showPwd() {\n      if (this.passwordType === 'password') {\n        this.passwordType = '';\n      } else {\n        this.passwordType = 'password';\n      }\n      this.$nextTick(() => {\n        this.$refs.password.focus();\n      });\n    },\n    handleLogin() {\n      this.$refs.loginForm.validate(valid => {\n        if (valid) {\n          this.loading = true;\n          // 模拟登录\n          setTimeout(() => {\n            this.loading = false;\n            this.$router.push({\n              path: this.redirect || '/'\n            });\n          }, 1000);\n        }\n      });\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[5267],{5267:function(e,s,o){o.r(s),o.d(s,{default:function(){return c}});var a=o(641),n=o(3751);const r={class:"login-container"},l={class:"svg-container"},t={class:"svg-container"};function i(e,s,o,i,d,p){const u=(0,a.g2)("svg-icon"),c=(0,a.g2)("el-input"),m=(0,a.g2)("el-form-item"),g=(0,a.g2)("el-button"),w=(0,a.g2)("el-form");return(0,a.uX)(),(0,a.CE)("div",r,[(0,a.bF)(w,{ref:"loginForm",model:d.loginForm,rules:d.loginRules,class:"login-form","auto-complete":"on","label-position":"left"},{default:(0,a.k6)(()=>[s[4]||(s[4]=(0,a.Lk)("div",{class:"title-container"},[(0,a.Lk)("h3",{class:"title"},"CRMEB管理后台")],-1)),(0,a.bF)(m,{prop:"username"},{default:(0,a.k6)(()=>[(0,a.Lk)("span",l,[(0,a.bF)(u,{"icon-class":"user"})]),(0,a.bF)(c,{ref:"username",modelValue:d.loginForm.username,"onUpdate:modelValue":s[0]||(s[0]=e=>d.loginForm.username=e),placeholder:"用户名",name:"username",type:"text",tabindex:"1","auto-complete":"on"},null,8,["modelValue"])]),_:1}),(0,a.bF)(m,{prop:"password"},{default:(0,a.k6)(()=>[(0,a.Lk)("span",t,[(0,a.bF)(u,{"icon-class":"password"})]),((0,a.uX)(),(0,a.Wv)(c,{key:d.passwordType,ref:"password",modelValue:d.loginForm.password,"onUpdate:modelValue":s[1]||(s[1]=e=>d.loginForm.password=e),type:d.passwordType,placeholder:"密码",name:"password",tabindex:"2","auto-complete":"on",onKeyup:(0,n.jR)(p.handleLogin,["enter","native"])},null,8,["modelValue","type","onKeyup"])),(0,a.Lk)("span",{class:"show-pwd",onClick:s[2]||(s[2]=(...e)=>p.showPwd&&p.showPwd(...e))},[(0,a.bF)(u,{"icon-class":"password"===d.passwordType?"eye":"eye-open"},null,8,["icon-class"])])]),_:1}),(0,a.bF)(g,{loading:d.loading,type:"primary",style:{width:"100%","margin-bottom":"30px"},onClick:(0,n.D$)(p.handleLogin,["prevent"])},{default:(0,a.k6)(()=>s[3]||(s[3]=[(0,a.eW)(" 登录 ",-1)])),_:1,__:[3]},8,["loading","onClick"])]),_:1,__:[4]},8,["model","rules"])])}o(4114);var d={name:"Login",data(){return{loginForm:{username:"admin",password:"123456"},loginRules:{username:[{required:!0,trigger:"blur",message:"请输入用户名"}],password:[{required:!0,trigger:"blur",message:"请输入密码"}]},loading:!1,passwordType:"password",redirect:void 0}},methods:{showPwd(){"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick(()=>{this.$refs.password.focus()})},handleLogin(){this.$refs.loginForm.validate(e=>{e&&(this.loading=!0,setTimeout(()=>{this.loading=!1,this.$router.push({path:this.redirect||"/"})},1e3))})}}},p=o(6262);const u=(0,p.A)(d,[["render",i],["__scopeId","data-v-9b1cb66c"]]);var c=u}}]);
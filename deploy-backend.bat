@echo off
echo ========================================
echo CRMEB电商系统后端部署脚本
echo ========================================

echo.
echo 第一步：上传JAR文件到服务器...
scp crmeb\crmeb-front\target\Crmeb-front.jar root@39.101.77.10:/www/wwwroot/crmeb/

echo.
echo 第二步：连接服务器并启动应用...
echo 请手动执行以下命令连接服务器：
echo ssh root@39.101.77.10
echo.
echo 然后在服务器上执行：
echo cd /www/wwwroot/crmeb/
echo nohup java -jar Crmeb-front.jar --spring.profiles.active=prod ^> crmeb.log 2^>^&1 ^&
echo.
echo 查看日志：
echo tail -f crmeb.log
echo.
echo ========================================
echo 部署脚本执行完成！
echo ========================================
pause

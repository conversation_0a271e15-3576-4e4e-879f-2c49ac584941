"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[5044],{5044:function(e,t,l){l.r(t),l.d(t,{default:function(){return y}});var a=l(641),s=l(3751),o=l(33);const i={class:"store-list"},c={class:"filter-container"},r={"slot-scope":"scope"},n={"slot-scope":"scope"},d={"slot-scope":"scope"},p={"slot-scope":"scope"},u={style:{color:"#f56c6c"}},h={"slot-scope":"scope"},m={"slot-scope":"scope"},g={"slot-scope":"scope"},k={"slot-scope":"scope"};function b(e,t,l,b,w,f){const _=(0,a.g2)("el-input"),y=(0,a.g2)("el-option"),F=(0,a.g2)("el-select"),v=(0,a.g2)("el-button"),L=(0,a.g2)("el-table-column"),C=(0,a.g2)("el-image"),S=(0,a.g2)("el-tag"),W=(0,a.g2)("el-table"),Q=(0,a.g2)("pagination"),x=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.Lk)("div",c,[(0,a.bF)(_,{modelValue:w.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>w.listQuery.keyword=e),placeholder:"请输入商品名称",style:{width:"200px"},class:"filter-item",onKeyup:(0,s.jR)(f.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),(0,a.bF)(F,{modelValue:w.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>w.listQuery.status=e),placeholder:"商品状态",clearable:"",style:{width:"120px"},class:"filter-item"},{default:(0,a.k6)(()=>[(0,a.bF)(y,{label:"上架",value:"1"}),(0,a.bF)(y,{label:"下架",value:"0"})]),_:1},8,["modelValue"]),(0,a.bF)(v,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:f.handleFilter},{default:(0,a.k6)(()=>t[4]||(t[4]=[(0,a.eW)(" 搜索 ",-1)])),_:1,__:[4]},8,["onClick"]),(0,a.bF)(v,{class:"filter-item",type:"primary",icon:"el-icon-plus",onClick:f.handleCreate},{default:(0,a.k6)(()=>t[5]||(t[5]=[(0,a.eW)(" 添加商品 ",-1)])),_:1,__:[5]},8,["onClick"])]),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(W,{data:w.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""},{default:(0,a.k6)(()=>[(0,a.bF)(L,{align:"center",label:"ID",width:"80"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",r,[(0,a.eW)((0,o.v_)(e.scope.row.id),1)])]),_:1}),(0,a.bF)(L,{label:"商品图片",width:"100"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",n,[(0,a.bF)(C,{style:{width:"60px",height:"60px"},src:e.scope.row.image,fit:"cover"},null,8,["src"])])]),_:1}),(0,a.bF)(L,{label:"商品名称","min-width":"200"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",d,[(0,a.eW)((0,o.v_)(e.scope.row.storeName),1)])]),_:1}),(0,a.bF)(L,{label:"价格",width:"100"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",p,[(0,a.Lk)("span",u,"¥"+(0,o.v_)(e.scope.row.price),1)])]),_:1}),(0,a.bF)(L,{label:"库存",width:"80"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",h,[(0,a.eW)((0,o.v_)(e.scope.row.stock),1)])]),_:1}),(0,a.bF)(L,{label:"销量",width:"80"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",m,[(0,a.eW)((0,o.v_)(e.scope.row.sales),1)])]),_:1}),(0,a.bF)(L,{label:"状态",width:"100"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",g,[(0,a.bF)(S,{type:1===e.scope.row.isShow?"success":"danger"},{default:(0,a.k6)(()=>[(0,a.eW)((0,o.v_)(1===e.scope.row.isShow?"上架":"下架"),1)]),_:1},8,["type"])])]),_:1}),(0,a.bF)(L,{label:"操作",align:"center",width:"200"},{default:(0,a.k6)(()=>[(0,a.Lk)("template",k,[(0,a.bF)(v,{size:"mini",type:"primary",onClick:t[2]||(t[2]=t=>f.handleEdit(e.scope.row))},{default:(0,a.k6)(()=>t[6]||(t[6]=[(0,a.eW)(" 编辑 ",-1)])),_:1,__:[6]}),(0,a.bF)(v,{size:"mini",type:1===e.scope.row.isShow?"danger":"success",onClick:t[3]||(t[3]=t=>f.handleStatus(e.scope.row))},{default:(0,a.k6)(()=>[(0,a.eW)((0,o.v_)(1===e.scope.row.isShow?"下架":"上架"),1)]),_:1},8,["type"])])]),_:1})]),_:1},8,["data"])),[[x,w.listLoading]]),(0,a.bo)((0,a.bF)(Q,{total:w.total,page:w.listQuery.page,limit:w.listQuery.limit,onPagination:f.getList},null,8,["total","page","limit","onPagination"]),[[s.aG,w.total>0]])])}var w={name:"StoreList",data(){return{list:[{id:1,storeName:"iPhone 15 Pro",image:"https://via.placeholder.com/60x60",price:7999,stock:100,sales:50,isShow:1},{id:2,storeName:"MacBook Pro",image:"https://via.placeholder.com/60x60",price:12999,stock:20,sales:10,isShow:1}],total:2,listLoading:!1,listQuery:{page:1,limit:20,keyword:"",status:""}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,setTimeout(()=>{this.listLoading=!1},500)},handleFilter(){this.listQuery.page=1,this.getList()},handleCreate(){this.$message.info("跳转到添加商品页面")},handleEdit(e){this.$message.info("编辑商品: "+e.storeName)},handleStatus(e){const t=1===e.isShow?"下架":"上架";this.$message.info(t+"商品: "+e.storeName)}}},f=l(6262);const _=(0,f.A)(w,[["render",b],["__scopeId","data-v-bc1ac09c"]]);var y=_}}]);
{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { defineStore } from 'pinia';\nimport Cookies from 'js-cookie';\nexport var useAppStore = defineStore('app', {\n  state: function state() {\n    return {\n      sidebar: {\n        opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,\n        withoutAnimation: false\n      },\n      device: 'desktop',\n      size: Cookies.get('size') || 'mini'\n    };\n  },\n  getters: {\n    sidebarOpened: function sidebarOpened(state) {\n      return state.sidebar.opened;\n    },\n    sidebarWithoutAnimation: function sidebarWithoutAnimation(state) {\n      return state.sidebar.withoutAnimation;\n    }\n  },\n  actions: {\n    toggleSideBar: function toggleSideBar() {\n      this.sidebar.opened = !this.sidebar.opened;\n      this.sidebar.withoutAnimation = false;\n      if (this.sidebar.opened) {\n        Cookies.set('sidebarStatus', 1);\n      } else {\n        Cookies.set('sidebarStatus', 0);\n      }\n    },\n    closeSideBar: function closeSideBar() {\n      var withoutAnimation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      Cookies.set('sidebarStatus', 0);\n      this.sidebar.opened = false;\n      this.sidebar.withoutAnimation = withoutAnimation;\n    },\n    toggleDevice: function toggleDevice(device) {\n      this.device = device;\n    },\n    setSize: function setSize(size) {\n      this.size = size;\n      Cookies.set('size', size);\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
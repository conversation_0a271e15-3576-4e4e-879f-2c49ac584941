{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nconst notificationRouter = {\n  path: '/notification',\n  component: Layout,\n  redirect: '/notification/index',\n  name: 'Notification',\n  meta: {\n    title: '通知管理',\n    icon: 'message'\n  },\n  children: [{\n    path: 'index',\n    component: () => import('@/views/notification/index'),\n    name: 'NotificationIndex',\n    meta: {\n      title: '通知管理',\n      icon: 'message'\n    }\n  }]\n};\nexport default notificationRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zbkj.common.constants.ExperienceRecordConstants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserExperienceRecord;
import com.zbkj.common.model.user.UserLevel;
import com.zbkj.common.utils.CrmebDateUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户经验值增强服务实现类
 * 提供更完善的经验值计算、等级升级和奖励机制
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class UserExperienceEnhancedServiceImpl implements UserExperienceEnhancedService {

    @Autowired
    private UserService userService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserExperienceRecordService experienceRecordService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    // Redis Key前缀
    private static final String USER_DAILY_EXP_KEY = "user:daily:exp:";
    private static final String USER_LEVEL_CACHE_KEY = "user:level:cache:";

    // 经验值获取规则配置
    private static final Map<String, Integer> EXPERIENCE_RULES = new HashMap<String, Integer>() {{
        put("register", 100);           // 注册奖励
        put("login", 5);               // 每日登录
        put("first_order", 200);       // 首次下单
        put("order_complete", 50);     // 订单完成
        put("comment", 20);            // 商品评价
        put("share", 10);              // 分享商品
        put("invite_register", 150);   // 邀请注册
        put("sign_continuous_7", 50);  // 连续签到7天
        put("sign_continuous_30", 200); // 连续签到30天
    }};

    // 每日经验值获取上限
    private static final Map<String, Integer> DAILY_EXP_LIMITS = new HashMap<String, Integer>() {{
        put("login", 5);               // 每日登录上限
        put("comment", 100);           // 每日评价上限
        put("share", 50);              // 每日分享上限
    }};

    @Override
    public Boolean addExperience(Integer userId, String type, Integer experience, String linkId, String remark) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(type) || ObjectUtil.isNull(experience) || experience <= 0) {
            throw new CrmebException("参数错误");
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        // 检查每日经验值获取限制
        if (!checkDailyExperienceLimit(userId, type, experience)) {
            log.warn("用户{}今日{}类型经验值已达上限", userId, type);
            return false;
        }

        // 计算实际获得的经验值（考虑VIP加成等）
        Integer actualExperience = calculateActualExperience(user, type, experience);

        return transactionTemplate.execute(status -> {
            try {
                // 更新用户经验值
                Integer newExperience = user.getExperience() + actualExperience;
                user.setExperience(newExperience);
                userService.updateById(user);

                // 记录经验值变更
                UserExperienceRecord record = new UserExperienceRecord();
                record.setUid(userId);
                record.setLinkId(linkId);
                record.setLinkType(getExperienceLinkType(type));
                record.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_ADD);
                record.setTitle(getExperienceTitle(type));
                record.setExperience(actualExperience);
                record.setBalance(newExperience);
                record.setMark(StrUtil.isNotBlank(remark) ? remark : getDefaultRemark(type, actualExperience));
                record.setStatus(ExperienceRecordConstants.EXPERIENCE_RECORD_STATUS_CREATE);
                record.setCreateTime(CrmebDateUtil.nowDateTime());
                experienceRecordService.save(record);

                // 更新每日经验值统计
                updateDailyExperienceCount(userId, type, actualExperience);

                // 检查并处理等级升级
                checkAndUpgradeLevel(user);

                // 清除用户等级缓存
                clearUserLevelCache(userId);

                return true;
            } catch (Exception e) {
                log.error("添加用户经验值失败，userId: {}, type: {}, experience: {}", userId, type, experience, e);
                status.setRollbackOnly();
                return false;
            }
        });
    }

    @Override
    public Boolean subtractExperience(Integer userId, String type, Integer experience, String linkId, String remark) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(type) || ObjectUtil.isNull(experience) || experience <= 0) {
            throw new CrmebException("参数错误");
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        if (user.getExperience() < experience) {
            throw new CrmebException("用户经验值不足");
        }

        return transactionTemplate.execute(status -> {
            try {
                // 更新用户经验值
                Integer newExperience = user.getExperience() - experience;
                user.setExperience(newExperience);
                userService.updateById(user);

                // 记录经验值变更
                UserExperienceRecord record = new UserExperienceRecord();
                record.setUid(userId);
                record.setLinkId(linkId);
                record.setLinkType(getExperienceLinkType(type));
                record.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_SUB);
                record.setTitle(getExperienceTitle(type));
                record.setExperience(experience);
                record.setBalance(newExperience);
                record.setMark(StrUtil.isNotBlank(remark) ? remark : getDefaultRemark(type, experience));
                record.setStatus(ExperienceRecordConstants.EXPERIENCE_RECORD_STATUS_CREATE);
                record.setCreateTime(CrmebDateUtil.nowDateTime());
                experienceRecordService.save(record);

                // 检查并处理等级降级
                checkAndDowngradeLevel(user);

                // 清除用户等级缓存
                clearUserLevelCache(userId);

                return true;
            } catch (Exception e) {
                log.error("扣减用户经验值失败，userId: {}, type: {}, experience: {}", userId, type, experience, e);
                status.setRollbackOnly();
                return false;
            }
        });
    }

    @Override
    public Map<String, Object> getUserLevelInfo(Integer userId) {
        String cacheKey = USER_LEVEL_CACHE_KEY + userId;
        Map<String, Object> cachedInfo = (Map<String, Object>) redisUtil.get(cacheKey);
        if (ObjectUtil.isNotNull(cachedInfo)) {
            return cachedInfo;
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        Map<String, Object> levelInfo = new HashMap<>();
        
        // 获取当前等级信息
        SystemUserLevel currentLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNull(currentLevel)) {
            // 获取默认等级
            List<SystemUserLevel> levels = systemUserLevelService.getUsableList();
            if (CollUtil.isNotEmpty(levels)) {
                currentLevel = levels.get(0);
            }
        }

        // 获取下一等级信息
        SystemUserLevel nextLevel = getNextLevel(currentLevel);

        levelInfo.put("currentLevel", currentLevel);
        levelInfo.put("nextLevel", nextLevel);
        levelInfo.put("currentExperience", user.getExperience());
        
        if (ObjectUtil.isNotNull(nextLevel)) {
            levelInfo.put("requiredExperience", nextLevel.getExperience());
            levelInfo.put("experienceToNext", nextLevel.getExperience() - user.getExperience());
            levelInfo.put("progressPercentage", calculateProgressPercentage(user.getExperience(), currentLevel.getExperience(), nextLevel.getExperience()));
        } else {
            levelInfo.put("requiredExperience", 0);
            levelInfo.put("experienceToNext", 0);
            levelInfo.put("progressPercentage", 100);
        }

        // 缓存5分钟
        redisUtil.set(cacheKey, levelInfo, 300L);
        
        return levelInfo;
    }

    @Override
    public Integer calculateOrderExperience(BigDecimal orderAmount) {
        if (ObjectUtil.isNull(orderAmount) || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }
        
        // 按订单金额计算经验值：每消费1元获得1经验值，最高100经验值
        Integer experience = orderAmount.intValue();
        return Math.min(experience, 100);
    }

    @Override
    public Boolean checkDailyExperienceLimit(Integer userId, String type, Integer experience) {
        if (!DAILY_EXP_LIMITS.containsKey(type)) {
            return true; // 没有限制的类型直接通过
        }

        String dailyKey = USER_DAILY_EXP_KEY + userId + ":" + type + ":" + CrmebDateUtil.nowDate();
        Integer todayExp = (Integer) redisUtil.get(dailyKey);
        todayExp = ObjectUtil.isNull(todayExp) ? 0 : todayExp;

        Integer limit = DAILY_EXP_LIMITS.get(type);
        return (todayExp + experience) <= limit;
    }

    @Override
    public void updateDailyExperienceCount(Integer userId, String type, Integer experience) {
        if (!DAILY_EXP_LIMITS.containsKey(type)) {
            return;
        }

        String dailyKey = USER_DAILY_EXP_KEY + userId + ":" + type + ":" + CrmebDateUtil.nowDate();
        Integer todayExp = (Integer) redisUtil.get(dailyKey);
        todayExp = ObjectUtil.isNull(todayExp) ? 0 : todayExp;
        
        // 设置过期时间为当天结束（24小时）
        long secondsUntilEndOfDay = 24 * 60 * 60; // 24小时
        redisUtil.set(dailyKey, todayExp + experience, secondsUntilEndOfDay);
    }

    @Override
    public Boolean checkAndUpgradeLevel(User user) {
        return userLevelService.upLevel(user);
    }

    @Override
    public Boolean checkAndDowngradeLevel(User user) {
        return userLevelService.downLevel(user);
    }

    /**
     * 计算实际获得的经验值（考虑VIP加成等）
     */
    private Integer calculateActualExperience(User user, String type, Integer baseExperience) {
        // 基础经验值
        Integer actualExperience = baseExperience;
        
        // VIP等级加成
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNotNull(userLevel) && userLevel.getGrade() > 1) {
            // 高等级用户获得额外10%经验值加成
            double bonus = 1.0 + (userLevel.getGrade() - 1) * 0.1;
            actualExperience = (int) (baseExperience * bonus);
        }
        
        return actualExperience;
    }

    /**
     * 获取经验值关联类型
     */
    private String getExperienceLinkType(String type) {
        switch (type) {
            case "register":
                return ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_SYSTEM;
            case "login":
            case "sign":
                return ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_SIGN;
            case "order_complete":
            case "first_order":
                return ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_ORDER;
            default:
                return ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_SYSTEM;
        }
    }

    /**
     * 获取经验值标题
     */
    private String getExperienceTitle(String type) {
        switch (type) {
            case "register":
                return "注册奖励";
            case "login":
                return "登录奖励";
            case "first_order":
                return "首次下单";
            case "order_complete":
                return "订单完成";
            case "comment":
                return "商品评价";
            case "share":
                return "分享商品";
            case "invite_register":
                return "邀请注册";
            case "sign":
                return "签到奖励";
            default:
                return "系统奖励";
        }
    }

    /**
     * 获取默认备注
     */
    private String getDefaultRemark(String type, Integer experience) {
        return StrUtil.format("{}获得{}经验值", getExperienceTitle(type), experience);
    }

    /**
     * 获取下一等级
     */
    private SystemUserLevel getNextLevel(SystemUserLevel currentLevel) {
        if (ObjectUtil.isNull(currentLevel)) {
            return null;
        }
        
        List<SystemUserLevel> levels = systemUserLevelService.getUsableList();
        if (CollUtil.isEmpty(levels)) {
            return null;
        }
        
        return levels.stream()
                .filter(level -> level.getGrade() > currentLevel.getGrade())
                .min((l1, l2) -> l1.getGrade().compareTo(l2.getGrade()))
                .orElse(null);
    }

    /**
     * 计算等级进度百分比
     */
    private Integer calculateProgressPercentage(Integer currentExp, Integer currentLevelExp, Integer nextLevelExp) {
        if (nextLevelExp <= currentLevelExp) {
            return 100;
        }
        
        int progress = (int) (((double) (currentExp - currentLevelExp) / (nextLevelExp - currentLevelExp)) * 100);
        return Math.max(0, Math.min(100, progress));
    }

    /**
     * 清除用户等级缓存
     */
    private void clearUserLevelCache(Integer userId) {
        String cacheKey = USER_LEVEL_CACHE_KEY + userId;
        redisUtil.delete(cacheKey);
    }
}
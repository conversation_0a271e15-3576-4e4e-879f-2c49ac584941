{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport store from '@/store';\nfunction authPermission(permission) {\n  var all_permission = '*:*:*';\n  var permissions = store.getters && store.getters.permissions;\n  if (permission && permission.length > 0) {\n    return permissions.some(function (v) {\n      return all_permission === v || v === permission;\n    });\n  } else {\n    return false;\n  }\n}\nfunction authRole(role) {\n  var super_admin = 'admin';\n  var roles = store.getters && store.getters.roles;\n  if (role && role.length > 0) {\n    return roles.some(function (v) {\n      return super_admin === v || v === role;\n    });\n  } else {\n    return false;\n  }\n}\nexport default {\n  // 验证用户是否具备某权限\n  hasPermi: function hasPermi(permission) {\n    return authPermission(permission);\n  },\n  // 验证用户是否含有指定权限，只需包含其中一个\n  hasPermiOr: function hasPermiOr(permissions) {\n    return permissions.some(function (item) {\n      return authPermission(item);\n    });\n  },\n  // 验证用户是否含有指定权限，必须全部拥有\n  hasPermiAnd: function hasPermiAnd(permissions) {\n    return permissions.every(function (item) {\n      return authPermission(item);\n    });\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
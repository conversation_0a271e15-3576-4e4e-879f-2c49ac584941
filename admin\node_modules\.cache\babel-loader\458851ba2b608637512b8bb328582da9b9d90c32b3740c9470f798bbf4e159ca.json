{"ast": null, "code": "import _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.to-string.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { storeStaffListApi } from '@/api/storePoint';\nimport { seckillListApi } from '@/api/marketing';\nimport { checkPermi } from '@/utils/permission'; // 权限判断函数\nimport Cookies from 'js-cookie';\n\n/**\r\n * @description 确定操作弹框\r\n */\nexport function modalSure(title) {\n  var _this = this;\n  return new Promise(function (resolve, reject) {\n    _this.$confirm(\"\\u786E\\u5B9A\".concat(title || '永久删除该数据'), '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n      customClass: 'sure-modal'\n    }).then(function () {\n      resolve();\n    }).catch(function () {\n      reject();\n      _this.$message({\n        type: 'info',\n        message: '已取消'\n      });\n    });\n  });\n}\n\n/**\r\n * @description 短信是否登录\r\n */\nexport function isLogin() {\n  return new Promise(function (resolve, reject) {\n    isLoginApi().then(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              resolve(res);\n            case 1:\n              return _context.a(2);\n          }\n        }, _callee);\n      }));\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }()).catch(function (res) {\n      reject(res);\n    });\n  });\n}\n\n/**\r\n * @description 核销员列表\r\n */\nexport function getStoreStaff() {\n  return new Promise(function (resolve, reject) {\n    if (checkPermi(['admin:system:staff:list'])) {\n      storeStaffListApi({\n        page: 1,\n        limit: 9999\n      }).then(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                localStorage.setItem('storeStaffList', res.list ? JSON.stringify(res.list) : []);\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    }\n  });\n}\n\n/**\r\n * @description 秒杀配置列表\r\n */\nexport function getSeckillList(status) {\n  return new Promise(function (resolve, reject) {\n    seckillListApi({\n      page: 1,\n      limit: 9999,\n      isDel: false,\n      status: status || null\n    }).then(/*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              resolve(res);\n            case 1:\n              return _context3.a(2);\n          }\n        }, _callee3);\n      }));\n      return function (_x3) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n  });\n}\n\n/**\r\n * @description 表格列表中删除最后一页中的唯一一个数据的操作\r\n */\nexport function handleDeleteTable(length, tableFrom) {\n  if (length === 1 && tableFrom.page > 1) return tableFrom.page = tableFrom.page - 1;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar orderRouter = {\n  path: '/order',\n  component: Layout,\n  redirect: '/order/index',\n  name: 'Order',\n  alwaysShow: true,\n  meta: {\n    title: '订单管理',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'index',\n    component: function component() {\n      return import('@/views/order/index');\n    },\n    name: 'OrderIndex',\n    meta: {\n      title: '订单管理'\n    }\n  }]\n};\nexport default orderRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
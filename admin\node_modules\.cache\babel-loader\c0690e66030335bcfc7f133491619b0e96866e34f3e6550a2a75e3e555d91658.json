{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nconst callbacks = {};\n\n/**\r\n * 加载一个远程脚本\r\n * @param {String} src 一个远程脚本\r\n * @param {Function} callback 回调\r\n */\nfunction loadScript(src, callback) {\n  const existingScript = document.getElementById(src);\n  const cb = callback || (() => {});\n  if (!existingScript) {\n    callbacks[src] = [];\n    const $script = document.createElement('script');\n    $script.src = src;\n    $script.id = src;\n    $script.async = 1;\n    document.body.appendChild($script);\n    const onEnd = 'onload' in $script ? stdOnEnd.bind($script) : ieOnEnd.bind($script);\n    onEnd($script);\n  }\n  callbacks[src].push(cb);\n  function stdOnEnd(script) {\n    script.onload = () => {\n      this.onerror = this.onload = null;\n      callbacks[src].forEach(item => {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n    script.onerror = () => {\n      this.onerror = this.onload = null;\n      cb(new Error(`Failed to load ${src}`), script);\n    };\n  }\n  function ieOnEnd(script) {\n    script.onreadystatechange = () => {\n      if (this.readyState !== 'complete' && this.readyState !== 'loaded') return;\n      this.onreadystatechange = null;\n      callbacks[src].forEach(item => {\n        item(null, script);\n      });\n      delete callbacks[src];\n    };\n  }\n}\n\n/**\r\n * 顺序加载一组远程脚本\r\n * @param {Array} list 一组远程脚本\r\n * @param {Function} cb 回调\r\n */\nexport function loadScriptQueue(list, cb) {\n  const first = list.shift();\n  list.length ? loadScript(first, () => loadScriptQueue(list, cb)) : loadScript(first, cb);\n}\nexport default loadScript;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
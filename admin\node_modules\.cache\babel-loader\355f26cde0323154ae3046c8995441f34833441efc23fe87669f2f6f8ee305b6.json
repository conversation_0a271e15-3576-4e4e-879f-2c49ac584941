{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\n/**\r\n * @description 短信发送记录 -- 列表\r\n */\nexport function smsLstApi(params) {\n  return request({\n    url: '/admin/pass/user/record',\n    method: 'get',\n    params: params\n  });\n}\n/**\r\n * @description 短信账户 -- 登录\r\n */\nexport function configApi(data) {\n  return request({\n    url: '/admin/pass/login',\n    method: 'post',\n    data: data\n  });\n}\n/**\r\n * @description 短信账户 -- 获取验证码\r\n */\nexport function captchaApi(params) {\n  return request({\n    url: \"/admin/pass/sendUserCode\",\n    method: 'get',\n    params: params\n  });\n}\n/**\r\n * @description 短信账户 -- 注册\r\n */\nexport function registerApi(data) {\n  return request({\n    url: '/admin/pass/register',\n    method: 'post',\n    data: data\n  });\n}\n/**\r\n * @description 短信账户 -- 是否登录\r\n */\nexport function isLoginApi() {\n  return request({\n    url: '/admin/pass/isLogin',\n    method: 'get'\n  });\n}\n/**\r\n * @description 短信账户 -- 退出登录\r\n */\nexport function logoutApi() {\n  return request({\n    url: '/admin/pass/logout',\n    method: 'get'\n  });\n}\n/**\r\n * @description 短信账户 -- 剩余条数\r\n */\nexport function smsNumberApi() {\n  return request({\n    url: '/admin/sms/logout',\n    method: 'get'\n  });\n}\n/**\r\n * @description 短信模板 -- 列表\r\n */\nexport function smsTempLstApi(params) {\n  return request({\n    url: '/admin/sms/temps',\n    method: 'get',\n    params: params\n  });\n}\n/**\r\n * @description 短信购买 -- 支付套餐\r\n */\nexport function smsPriceApi(params) {\n  return request({\n    url: '/admin/pass/meal/list',\n    method: 'get',\n    params: params\n  });\n}\n/**\r\n * @description 短信购买 -- 支付码\r\n */\nexport function payCodeApi(data) {\n  return request({\n    url: '/admin/pass/meal/code',\n    method: 'post',\n    data: data\n  });\n}\n/**\r\n * @description 短信模板 -- 添加表单\r\n */\nexport function tempCreateApi(data) {\n  return request({\n    url: '/admin/sms/temp/apply',\n    method: 'post',\n    data: data\n  });\n}\n/**\r\n * @description 短信 -- 用户信息\r\n */\nexport function smsInfoApi() {\n  return request({\n    url: '/admin/pass/info',\n    method: 'get'\n  });\n}\n\n/**\r\n * @description 短信 -- 短信提醒开关保存\r\n */\nexport function smsSaveApi(params) {\n  return request({\n    url: '/admin/sms/config/save',\n    method: 'post',\n    params: params\n  });\n}\n\n/**\r\n * @description 短信 -- 修改密码\r\n */\nexport function updatePasswordApi(data) {\n  return request({\n    url: '/admin/pass/update/password',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 短信 -- 修改手机号\r\n */\nexport function updateHoneApi(data) {\n  return request({\n    url: '/admin/pass/update/phone',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 一号通 -- 服务开通\r\n */\nexport function serviceOpenApi(data) {\n  return request({\n    url: '/admin/pass/service/open',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 一号通 -- 电子面单模板\r\n */\nexport function exportTempApi(params) {\n  return request({\n    url: '/admin/express/template',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * @description 全部物流公司\r\n */\nexport function expressAllApi(params) {\n  return request({\n    url: 'admin/express/all',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * @description 修改签名\r\n */\nexport function smsSignApi(data) {\n  return request({\n    url: 'admin/sms/modify/sign',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 修改手机号验证账号密码\r\n */\nexport function phoneValidatorApi(data) {\n  return request({\n    url: 'admin/pass/update/phone/validator',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 一号通 商家寄件 快递列表\r\n */\nexport function shipmentExpressApi() {\n  return request({\n    url: '/admin/pass/shipment/express',\n    method: 'get'\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
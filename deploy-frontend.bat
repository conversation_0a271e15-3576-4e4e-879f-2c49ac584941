@echo off
echo ========================================
echo 正在部署CRMEB前端到服务器...
echo ========================================

REM 设置服务器信息（请根据实际情况修改）
set SERVER_IP=your-server-ip
set SERVER_USER=root

echo 1. 正在上传管理后台文件...
scp -r admin\dist\* %SERVER_USER%@%SERVER_IP%:/www/wwwroot/admin/

echo 2. 正在上传静态前端文件...
scp -r web-frontend\* %SERVER_USER%@%SERVER_IP%:/www/wwwroot/

echo 3. 正在设置文件权限...
ssh %SERVER_USER%@%SERVER_IP% "chown -R nginx:nginx /www/wwwroot/ && chmod -R 755 /www/wwwroot/"

echo ========================================
echo 前端部署完成！
echo 请访问：https://chekunru.top
echo 管理后台：https://chekunru.top/admin
echo ========================================
pause

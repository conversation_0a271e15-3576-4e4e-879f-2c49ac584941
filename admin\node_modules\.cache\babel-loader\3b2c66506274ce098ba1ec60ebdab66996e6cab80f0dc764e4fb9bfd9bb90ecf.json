{"ast": null, "code": "import { createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = [\"xlink:href\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"svg\", {\n    class: _normalizeClass($options.svgClass),\n    \"aria-hidden\": \"true\"\n  }, [_createElementVNode(\"use\", {\n    \"xlink:href\": $options.iconName\n  }, null, 8, _hoisted_1)], 2);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
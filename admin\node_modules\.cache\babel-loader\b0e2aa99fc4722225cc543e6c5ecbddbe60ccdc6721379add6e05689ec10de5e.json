{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n// 请求接口地址 - 小程序版本使用固定地址\nvar VUE_APP_API_URL = process.env.VUE_APP_BASE_API || 'http://localhost:20500';\nvar VUE_APP_WS_URL = process.env.VUE_APP_WS_URL || 'ws://localhost:20500';\nvar SettingMer = {\n  // 服务器地址\n  httpUrl: VUE_APP_API_URL,\n  // 接口请求地址\n  apiBaseURL: VUE_APP_API_URL + '/api/',\n  // socket连接\n  wsSocketUrl: VUE_APP_WS_URL\n};\nexport default SettingMer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
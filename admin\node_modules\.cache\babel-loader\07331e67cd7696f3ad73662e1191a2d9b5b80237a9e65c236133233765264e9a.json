{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { storeStaffListApi } from '@/api/storePoint';\nimport { seckillListApi } from '@/api/marketing';\nimport { checkPermi } from '@/utils/permission'; // 权限判断函数\nimport Cookies from 'js-cookie';\n\n/**\r\n * @description 确定操作弹框\r\n */\nexport function modalSure(title) {\n  return new Promise((resolve, reject) => {\n    this.$confirm(`确定${title || '永久删除该数据'}`, '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning',\n      customClass: 'sure-modal'\n    }).then(() => {\n      resolve();\n    }).catch(() => {\n      reject();\n      this.$message({\n        type: 'info',\n        message: '已取消'\n      });\n    });\n  });\n}\n\n/**\r\n * @description 短信是否登录\r\n */\nexport function isLogin() {\n  return new Promise((resolve, reject) => {\n    isLoginApi().then(async res => {\n      resolve(res);\n    }).catch(res => {\n      reject(res);\n    });\n  });\n}\n\n/**\r\n * @description 核销员列表\r\n */\nexport function getStoreStaff() {\n  return new Promise((resolve, reject) => {\n    if (checkPermi(['admin:system:staff:list'])) {\n      storeStaffListApi({\n        page: 1,\n        limit: 9999\n      }).then(async res => {\n        localStorage.setItem('storeStaffList', res.list ? JSON.stringify(res.list) : []);\n      });\n    }\n  });\n}\n\n/**\r\n * @description 秒杀配置列表\r\n */\nexport function getSeckillList(status) {\n  return new Promise((resolve, reject) => {\n    seckillListApi({\n      page: 1,\n      limit: 9999,\n      isDel: false,\n      status: status || null\n    }).then(async res => {\n      resolve(res);\n    });\n  });\n}\n\n/**\r\n * @description 表格列表中删除最后一页中的唯一一个数据的操作\r\n */\nexport function handleDeleteTable(length, tableFrom) {\n  if (length === 1 && tableFrom.page > 1) return tableFrom.page = tableFrom.page - 1;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
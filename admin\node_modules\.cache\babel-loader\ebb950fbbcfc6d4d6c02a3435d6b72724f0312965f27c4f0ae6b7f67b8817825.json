{"ast": null, "code": "import _typeof from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.constructor.js\";\nimport \"core-js/modules/es.regexp.dot-all.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.sticky.js\";\nimport \"core-js/modules/es.regexp.test.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.match.js\";\nimport \"core-js/modules/es.string.search.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport WechatJSSDK from 'wechat-jssdk/dist/client.umd';\nimport { getWechatConfig, wechatAuth } from '@/api/wxApi';\nimport { getToken, removeToken, setToken } from '@/utils/auth';\nimport { parseQuery } from '@/utils';\nimport Cookies from 'js-cookie';\nvar STATE_KEY = 'wx_authorize_state';\nimport store from '@/store';\nvar WX_AUTH = 'wx_auth';\nvar BACK_URL = 'login_back_url';\nvar LOGINTYPE = 'loginType';\nvar instance;\nvar wechatObj;\nvar LONGITUDE = 'user_longitude';\nvar LATITUDE = 'user_latitude';\nvar WECHAT_SCRIPT_URL = '//res.wx.qq.com/open/js/jweixin-1.6.0.js';\n\n/**\r\n * 是否是微信\r\n */\nexport function isWeixin() {\n  return navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1;\n}\n\n/**\r\n * 是否是移动端\r\n */\nexport function isPhone() {\n  return /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent);\n}\nexport default function wechat() {\n  return new Promise(function (resolve, reject) {\n    if (instance) return resolve(instance);\n    getWechatConfig().then(function (res) {\n      var _wx = WechatJSSDK(res);\n      wechatObj = _wx;\n      _wx.initialize().then(function () {\n        instance = _wx.wx;\n        instance.initConfig = res;\n        resolve(instance);\n      }).catch(reject);\n    }).catch(function (err) {\n      reject(err);\n    });\n  });\n}\nexport function loginByWxCode(code) {\n  return new Promise(function (resolve, reject) {\n    var loginType = getToken();\n    wechatAuth(code).then(function (res) {\n      store.commit('SET_TOKEN', res.token);\n      setToken(res.token);\n      Cookies.set(WX_AUTH, code);\n      resolve(res);\n    }).catch(function (err) {\n      reject(err);\n    });\n  });\n}\nexport function getWXCodeByUrl(path, step) {\n  if (getToken()) return;\n  generatorWxUrl(path, step);\n}\nexport function generatorWxUrl(path, step) {\n  wechat().then(function (wx) {\n    window.location.href = getAuthUrl(wx.initConfig, path, step);\n  }).catch(function (err) {\n    reject(err);\n  });\n}\nfunction getAuthUrl(config, path, step) {\n  var finalUrl = \"https://open.weixin.qq.com/connect/oauth2/authorize?appid=\".concat(config.appId, \"&redirect_uri=\").concat(encodeURIComponent(path), \"&response_type=code&scope=snsapi_base&state=\").concat(step, \"#wechat_redirect\");\n  return finalUrl;\n}\nfunction getQueryString(name) {\n  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');\n  var reg_rewrite = new RegExp('(^|/)' + name + '/([^/]*)(/|$)', 'i');\n  var r = window.location.search.substr(1).match(reg);\n  var q = window.location.pathname.substr(1).match(reg_rewrite);\n  if (r != null) {\n    return unescape(r[2]);\n  } else if (q != null) {\n    return unescape(q[2]);\n  } else {\n    return null;\n  }\n}\n\n/**\r\n * 公众号事件\r\n * @param name 事件名\r\n * @param config 配置\r\n * @returns {Promise<unknown>}\r\n */\nexport function wechatEvevt(name, config) {\n  return new Promise(function (resolve, reject) {\n    var wx;\n    var configDefault = {\n      fail: function fail(res) {\n        if (wx) return reject({\n          is_ready: true,\n          wx: wx\n        });\n        getWechatConfig().then(function (res) {\n          wechatObj.signSignature({\n            nonceStr: res.nonceStr,\n            signature: res.signature,\n            timestamp: res.timestamp\n          });\n          wx = wechatObj.getOriginalWx();\n          reject({\n            is_ready: true,\n            wx: wx\n          });\n        });\n      },\n      success: function success(res) {\n        resolve(res);\n      },\n      cancel: function cancel(err) {\n        reject(err);\n      },\n      complete: function complete(err) {\n        reject(err);\n      }\n    };\n    Object.assign(configDefault, config);\n    getWechatConfig().then(function (res) {\n      var _wx = WechatJSSDK(res);\n      _wx.initialize().then(function () {\n        instance = _wx.getOriginalWx();\n        instance.ready(function () {\n          if (_typeof(name) === 'object') {\n            name.forEach(function (item) {\n              instance[item] && instance[item](configDefault);\n            });\n          } else instance[name] && instance[name](configDefault);\n        });\n      });\n    });\n  });\n}\nexport function ready() {\n  return new Promise(function (resolve) {\n    if (typeof instance !== 'undefined') {\n      instance.ready(function () {\n        resolve(instance);\n      });\n    } else {\n      getWechatConfig().then(function (res) {\n        var _wx = WechatJSSDK(res);\n        _wx.initialize().then(function () {\n          instance = _wx.wx;\n          instance.ready(function () {\n            resolve(instance);\n          });\n        });\n      });\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport Vue from 'vue';\nimport SvgIcon from '@/components/SvgIcon'; // svg component\n\n// register globally\nVue.component('svg-icon', SvgIcon);\nconst req = require.context('./svg', false, /\\.svg$/);\nconst requireAll = requireContext => requireContext.keys().map(requireContext);\nrequireAll(req);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
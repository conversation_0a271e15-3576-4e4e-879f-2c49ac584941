{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nconst components = require.context('./', false, /\\.vue$/u);\nexport default {\n  install(app) {\n    components.keys().forEach(item => {\n      const component = components(item).default;\n      if (component && component.name) {\n        app.component(component.name, component);\n      }\n    });\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
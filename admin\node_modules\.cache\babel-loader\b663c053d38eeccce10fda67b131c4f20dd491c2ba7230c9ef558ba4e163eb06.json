{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from '@/store';\nexport default {\n  inserted(el, binding, vnode) {\n    const {\n      value\n    } = binding;\n    const super_admin = 'admin';\n    const roles = store.state.user.name;\n    if (value && value instanceof Array && value.length > 0) {\n      const roleFlag = value;\n      const hasRole = roles.some(role => {\n        return super_admin === role || roleFlag.includes(role);\n      });\n      if (!hasRole) {\n        el.parentNode && el.parentNode.removeChild(el);\n      }\n    } else {\n      throw new Error(`请设置角色权限标签值\"`);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
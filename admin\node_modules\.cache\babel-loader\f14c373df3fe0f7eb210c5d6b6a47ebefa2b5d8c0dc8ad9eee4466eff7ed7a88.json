{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"redirect-page\"\n};\nconst _hoisted_2 = {\n  class: \"loading-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_loading_spinner = _resolveComponent(\"el-loading-spinner\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_loading_spinner), _cache[0] || (_cache[0] = _createElementVNode(\"p\", null, \"页面跳转中...\", -1))])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import _regenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport { saveAs as _saveAs } from 'file-saver';\nimport axios from 'axios';\nimport { getToken } from '@/utils/auth';\nimport { ElMessage } from 'element-plus';\nvar baseURL = process.env.VUE_APP_BASE_API;\nexport default {\n  name: function name(_name) {\n    var _this = this;\n    var isDelete = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n    var url = baseURL + '/common/download?fileName=' + encodeURI(_name) + '&delete=' + isDelete;\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n        var isLogin, blob;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              _context.n = 1;\n              return _this.blobValidate(res.data);\n            case 1:\n              isLogin = _context.v;\n              if (isLogin) {\n                blob = new Blob([res.data]);\n                _this.saveAs(blob, decodeURI(res.headers['download-filename']));\n              } else {\n                ElMessage.error('无效的会话，或者会话已过期，请重新登录。');\n              }\n            case 2:\n              return _context.a(2);\n          }\n        }, _callee);\n      }));\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  },\n  resource: function resource(_resource) {\n    var _this2 = this;\n    var url = baseURL + '/common/download/resource?resource=' + encodeURI(_resource);\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(/*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n        var isLogin, blob;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              _context2.n = 1;\n              return _this2.blobValidate(res.data);\n            case 1:\n              isLogin = _context2.v;\n              if (isLogin) {\n                blob = new Blob([res.data]);\n                _this2.saveAs(blob, decodeURI(res.headers['download-filename']));\n              } else {\n                ElMessage.error('无效的会话，或者会话已过期，请重新登录。');\n              }\n            case 2:\n              return _context2.a(2);\n          }\n        }, _callee2);\n      }));\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n  },\n  zip: function zip(url, name) {\n    var _this3 = this;\n    var url = baseURL + url;\n    axios({\n      method: 'get',\n      url: url,\n      responseType: 'blob',\n      headers: {\n        Authorization: 'Bearer ' + getToken()\n      }\n    }).then(/*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n        var isLogin, blob;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              _context3.n = 1;\n              return _this3.blobValidate(res.data);\n            case 1:\n              isLogin = _context3.v;\n              if (isLogin) {\n                blob = new Blob([res.data], {\n                  type: 'application/zip'\n                });\n                _this3.saveAs(blob, name);\n              } else {\n                Message.error('无效的会话，或者会话已过期，请重新登录。');\n              }\n            case 2:\n              return _context3.a(2);\n          }\n        }, _callee3);\n      }));\n      return function (_x3) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n  },\n  saveAs: function saveAs(text, name, opts) {\n    _saveAs(text, name, opts);\n  },\n  blobValidate: function blobValidate(data) {\n    return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n      var text, _t;\n      return _regenerator().w(function (_context4) {\n        while (1) switch (_context4.p = _context4.n) {\n          case 0:\n            _context4.p = 0;\n            _context4.n = 1;\n            return data.text();\n          case 1:\n            text = _context4.v;\n            JSON.parse(text);\n            return _context4.a(2, false);\n          case 2:\n            _context4.p = 2;\n            _t = _context4.v;\n            return _context4.a(2, true);\n        }\n      }, _callee4, null, [[0, 2]]);\n    }))();\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
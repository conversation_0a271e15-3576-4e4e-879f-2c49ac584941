#!/bin/bash

echo "========================================"
echo "CRMEB电商系统后端部署脚本"
echo "========================================"

echo ""
echo "第一步：上传JAR文件到服务器..."
scp crmeb/crmeb-front/target/Crmeb-front.jar root@39.101.77.10:/www/wwwroot/crmeb/

echo ""
echo "第二步：连接服务器并启动应用..."
ssh root@39.101.77.10 << 'EOF'
cd /www/wwwroot/crmeb/

# 停止现有的Java进程（如果有）
pkill -f "Crmeb-front.jar" || true

# 启动新的应用
nohup java -jar Crmeb-front.jar --spring.profiles.active=prod > crmeb.log 2>&1 &

echo "应用已启动，PID: $!"
echo "查看日志: tail -f /www/wwwroot/crmeb/crmeb.log"
EOF

echo ""
echo "========================================"
echo "部署脚本执行完成！"
echo "========================================"

{"ast": null, "code": "import { renderSlot as _renderSlot, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"auth-redirect-component\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"default\", {}, () => [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"default-content\"\n  }, [_createElementVNode(\"p\", null, \"Auth-redirect 组件\")], -1))], true)]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
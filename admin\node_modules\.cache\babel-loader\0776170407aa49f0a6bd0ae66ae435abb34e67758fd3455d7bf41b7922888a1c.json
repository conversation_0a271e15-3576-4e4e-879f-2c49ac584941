{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from '@/store';\nimport { fileImageApi } from '@/api/systemSetting';\n/**\r\n * 根据需求过滤掉treeData中的child.length === 0的数据\r\n * @param treeData\r\n * @returns {Uint8Array | BigInt64Array | any[] | Float64Array | Int8Array | Float32Array | Int32Array | Uint32Array | Uint8ClampedArray | BigUint64Array | Int16Array | Uint16Array}\r\n */\nexport function clearTreeData(treeData) {\n  return treeData.map(item => {\n    if (item.child.length === 0) {\n      delete item.child;\n    } else {\n      clearTreeData(item.child);\n    }\n    return item;\n  });\n}\n/**\r\n * 上传图片、视频\r\n * @param formData\r\n * @param data\r\n */\nexport function uploadImage(formData, data) {\n  return new Promise((resolve, reject) => {\n    fileImageApi(formData, data).then(res => {\n      // Message.success('上传成功');\n      return resolve(res);\n    }).catch(res => {\n      reject();\n    });\n  });\n}\nexport function addTreeListLabel(treeData) {\n  // 因树形控件在slot-scope模式下显示字段只能为label为此自定义添加label字段和child=children\n  return treeData.map(item => {\n    if ((item.name === '设置' || item.name === '管理员列表' || item.name === '身份管理' || item.name === '管理权限' || item.name === '管理员列表' || item.name === '权限规则') && store.getters.name !== 'admin') {\n      item.disabled = true;\n    }\n    item.label = item.name;\n    return item;\n  });\n}\nexport function addTreeListLabelForCasCard(treeData, child) {\n  treeData.map(item => {\n    if ((item.name === '设置' || item.name === '管理员列表' || item.name === '身份管理' || item.name === '管理权限' || item.name === '管理员列表' || item.name === '权限规则') && store.getters.name !== 'admin') {\n      item.disabled = true;\n    }\n    item.label = item.name;\n    return item;\n  });\n}\n\n//加法函数，用来得到精确的加法结果\n//说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。\n//调用：$h.Add(arg1,arg2)\n//返回值：arg1加上arg2的精确结果\nexport function Add(arg1, arg2) {\n  arg2 = parseFloat(arg2);\n  var r1, r2, m;\n  try {\n    r1 = arg1.toString().split('.')[1].length;\n  } catch (e) {\n    r1 = 0;\n  }\n  try {\n    r2 = arg2.toString().split('.')[1].length;\n  } catch (e) {\n    r2 = 0;\n  }\n  m = Math.pow(100, Math.max(r1, r2));\n  return (this.Mul(arg1, m) + this.Mul(arg2, m)) / m;\n}\n\n//乘法函数，用来得到精确的乘法结果\n//说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。\n//调用：$h.Mul(arg1,arg2)\n//返回值：arg1乘以arg2的精确结果\nexport function Mul(arg1, arg2) {\n  arg1 = parseFloat(arg1);\n  arg2 = parseFloat(arg2);\n  var m = 0,\n    s1 = arg1.toString(),\n    s2 = arg2.toString();\n  try {\n    m += s1.split('.')[1].length;\n  } catch (e) {}\n  try {\n    m += s2.split('.')[1].length;\n  } catch (e) {}\n  return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m);\n}\n\n//替换安全域名\nexport function setDomain(url) {\n  url = url ? url.toString() : '';\n  // 正则替换存在的转义符\n  url = url.replace(/\\\\/g, '');\n  url = window.location.protocol === 'https:' ? url.replace('http://', 'https://') : url;\n  if (url.startsWith('src=\"')) {\n    url = url.replaceAll('src=\"', '');\n  }\n  if (url.startsWith('//img') && window.location.protocol === 'https:') {\n    url = url.replace('//img', 'https://img');\n  }\n  return url;\n}\n\n// 过滤富文本中的 img 相对路径访问\nexport function replaceImgSrcHttps(content) {\n  return content.replaceAll('src=\"//', 'src=\"https://');\n}\n\n/**\r\n *计算table固定高度\r\n */\nexport function getTableHeight(height) {\n  let windowHeight = document.documentElement.clientHeight || document.body.clientHeight;\n  let herderHeight = 100;\n  let footerHeight = 20;\n  return windowHeight - herderHeight - footerHeight - height;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
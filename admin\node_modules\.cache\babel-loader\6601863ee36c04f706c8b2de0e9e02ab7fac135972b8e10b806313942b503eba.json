{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport store from '@/store';\nexport default {\n  inserted: function inserted(el, binding, vnode) {\n    // 当被绑定的元素插入到 DOM 中时……\n    var value = binding.value;\n    var all_permission = '*:*:*';\n    //超管标识\n    var permissions = store.getters && store.getters.permissions;\n    //从getters中取出从接口请求到的权限标识数组\n    if (value && value instanceof Array && value.length > 0) {\n      //value为指令的绑定值，并且要求是一个非空数组\n      var permissionFlag = value;\n      var hasPermissions = permissions.some(function (permission) {\n        //some() 方法用于检测数组中的元素是否满足指定条件（函数提供）\n        //如果是超管或者其他管理员有对应的权限标识\n        return all_permission === permission || permissionFlag.includes(permission);\n        //检测数组 permissionFlag 是否包含 permission\n      });\n      if (!hasPermissions) {\n        el.parentNode && el.parentNode.removeChild(el);\n        //否则就删除该节点，体现在页面上就是没有按钮对应的权限标识就不显示该按钮\n      }\n    } else {\n      throw new Error(\"\\u8BF7\\u8BBE\\u7F6E\\u64CD\\u4F5C\\u6743\\u9650\\u6807\\u7B7E\\u503C\");\n      //页面上使用v-hasPermi没有传值的情况下给的提示\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport Layout from '@/layout';\nvar distributionRouter = {\n  path: '/distribution',\n  component: Layout,\n  redirect: '/distribution/distributionconfig',\n  name: 'Distribution',\n  meta: {\n    title: '分销',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'index',\n    component: function component() {\n      return import('@/views/distribution/index');\n    },\n    name: 'distributionIndex',\n    meta: {\n      title: '分销员管理',\n      icon: ''\n    }\n  }, {\n    path: 'distributionconfig',\n    component: function component() {\n      return import('@/views/distribution/config/index');\n    },\n    name: 'distributionConfig',\n    meta: {\n      title: '分销配置',\n      icon: ''\n    }\n  }]\n};\nexport default distributionRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
package com.zbkj.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zbkj.common.constants.ExperienceRecordConstants;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.system.SystemUserLevel;
import com.zbkj.common.model.user.User;
import com.zbkj.common.model.user.UserLevel;
import com.zbkj.common.utils.CrmebDateUtil;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户等级增强服务实现类
 * 提供更完善的等级管理、升级奖励和特权功能
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class UserLevelEnhancedServiceImpl implements UserLevelEnhancedService {

    @Autowired
    private UserService userService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserExperienceEnhancedService experienceService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    // Redis Key前缀
    private static final String USER_LEVEL_PRIVILEGE_KEY = "user:level:privilege:";
    private static final String LEVEL_UPGRADE_REWARD_KEY = "level:upgrade:reward:";

    // 等级特权配置
    private static final Map<Integer, Map<String, Object>> LEVEL_PRIVILEGES = new HashMap<Integer, Map<String, Object>>() {{
        // VIP1特权
        put(1, new HashMap<String, Object>() {{
            put("experienceBonus", 1.1);        // 经验值加成10%
            put("integralBonus", 1.1);          // 积分加成10%
            put("freeShipping", false);         // 免邮特权
            put("priorityService", false);      // 优先客服
            put("exclusiveDiscount", 0.95);     // 专属折扣5%
            put("monthlyGifts", false);         // 每月礼品
        }});
        
        // VIP2特权
        put(2, new HashMap<String, Object>() {{
            put("experienceBonus", 1.2);        // 经验值加成20%
            put("integralBonus", 1.2);          // 积分加成20%
            put("freeShipping", true);          // 免邮特权
            put("priorityService", false);      // 优先客服
            put("exclusiveDiscount", 0.9);      // 专属折扣10%
            put("monthlyGifts", false);         // 每月礼品
        }});
        
        // VIP3特权
        put(3, new HashMap<String, Object>() {{
            put("experienceBonus", 1.3);        // 经验值加成30%
            put("integralBonus", 1.3);          // 积分加成30%
            put("freeShipping", true);          // 免邮特权
            put("priorityService", true);       // 优先客服
            put("exclusiveDiscount", 0.85);     // 专属折扣15%
            put("monthlyGifts", true);          // 每月礼品
        }});
        
        // VIP4特权
        put(4, new HashMap<String, Object>() {{
            put("experienceBonus", 1.4);        // 经验值加成40%
            put("integralBonus", 1.4);          // 积分加成40%
            put("freeShipping", true);          // 免邮特权
            put("priorityService", true);       // 优先客服
            put("exclusiveDiscount", 0.8);      // 专属折扣20%
            put("monthlyGifts", true);          // 每月礼品
        }});
        
        // VIP5特权
        put(5, new HashMap<String, Object>() {{
            put("experienceBonus", 1.5);        // 经验值加成50%
            put("integralBonus", 1.5);          // 积分加成50%
            put("freeShipping", true);          // 免邮特权
            put("priorityService", true);       // 优先客服
            put("exclusiveDiscount", 0.75);     // 专属折扣25%
            put("monthlyGifts", true);          // 每月礼品
        }});
    }};

    // 升级奖励配置
    private static final Map<Integer, Map<String, Object>> UPGRADE_REWARDS = new HashMap<Integer, Map<String, Object>>() {{
        put(1, new HashMap<String, Object>() {{
            put("integral", 100);               // 积分奖励
            put("coupon", "WELCOME_VIP1");      // 优惠券
            put("experience", 50);              // 额外经验值
        }});
        
        put(2, new HashMap<String, Object>() {{
            put("integral", 200);
            put("coupon", "WELCOME_VIP2");
            put("experience", 100);
        }});
        
        put(3, new HashMap<String, Object>() {{
            put("integral", 300);
            put("coupon", "WELCOME_VIP3");
            put("experience", 150);
        }});
        
        put(4, new HashMap<String, Object>() {{
            put("integral", 500);
            put("coupon", "WELCOME_VIP4");
            put("experience", 200);
        }});
        
        put(5, new HashMap<String, Object>() {{
            put("integral", 1000);
            put("coupon", "WELCOME_VIP5");
            put("experience", 300);
        }});
    }};

    @Override
    public Boolean upgradeUserLevel(Integer userId, Integer targetLevelId, String reason) {
        if (ObjectUtil.isNull(userId) || ObjectUtil.isNull(targetLevelId)) {
            throw new CrmebException("参数错误");
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        SystemUserLevel targetLevel = systemUserLevelService.getByLevelId(targetLevelId);
        if (ObjectUtil.isNull(targetLevel)) {
            throw new CrmebException("目标等级不存在");
        }

        SystemUserLevel currentLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNotNull(currentLevel) && targetLevel.getGrade() <= currentLevel.getGrade()) {
            throw new CrmebException("目标等级不能低于或等于当前等级");
        }

        return transactionTemplate.execute(status -> {
            try {
                // 更新用户等级
                Integer oldLevel = user.getLevel();
                user.setLevel(targetLevelId);
                userService.updateById(user);

                // 记录等级变更
                UserLevel levelRecord = new UserLevel();
                levelRecord.setUid(userId);
                levelRecord.setLevelId(targetLevelId);
                levelRecord.setGrade(targetLevel.getGrade());
                levelRecord.setDiscount(targetLevel.getDiscount());
                levelRecord.setStatus(true);
                levelRecord.setMark(StrUtil.isNotBlank(reason) ? reason : "系统升级");
                levelRecord.setRemind(false);
                levelRecord.setCreateTime(CrmebDateUtil.nowDateTime());
                userLevelService.save(levelRecord);

                // 发放升级奖励
                giveUpgradeRewards(user, targetLevel.getGrade());

                // 清除等级特权缓存
                clearUserLevelPrivilegeCache(userId);

                log.info("用户等级升级成功：userId={}, oldLevel={}, newLevel={}, reason={}", 
                        userId, oldLevel, targetLevelId, reason);
                
                return true;
            } catch (Exception e) {
                log.error("用户等级升级失败：userId={}, targetLevel={}", userId, targetLevelId, e);
                status.setRollbackOnly();
                return false;
            }
        });
    }

    @Override
    public Boolean downgradeUserLevel(Integer userId, Integer targetLevelId, String reason) {
        if (ObjectUtil.isNull(userId) || ObjectUtil.isNull(targetLevelId)) {
            throw new CrmebException("参数错误");
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        SystemUserLevel targetLevel = systemUserLevelService.getByLevelId(targetLevelId);
        if (ObjectUtil.isNull(targetLevel)) {
            throw new CrmebException("目标等级不存在");
        }

        SystemUserLevel currentLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNotNull(currentLevel) && targetLevel.getGrade() >= currentLevel.getGrade()) {
            throw new CrmebException("目标等级不能高于或等于当前等级");
        }

        return transactionTemplate.execute(status -> {
            try {
                // 更新用户等级
                Integer oldLevel = user.getLevel();
                user.setLevel(targetLevelId);
                userService.updateById(user);

                // 记录等级变更
                UserLevel levelRecord = new UserLevel();
                levelRecord.setUid(userId);
                levelRecord.setLevelId(targetLevelId);
                levelRecord.setGrade(targetLevel.getGrade());
                levelRecord.setDiscount(targetLevel.getDiscount());
                levelRecord.setStatus(true);
                levelRecord.setMark(StrUtil.isNotBlank(reason) ? reason : "系统降级");
                levelRecord.setRemind(false);
                levelRecord.setCreateTime(CrmebDateUtil.nowDateTime());
                userLevelService.save(levelRecord);

                // 清除等级特权缓存
                clearUserLevelPrivilegeCache(userId);

                log.info("用户等级降级成功：userId={}, oldLevel={}, newLevel={}, reason={}", 
                        userId, oldLevel, targetLevelId, reason);
                
                return true;
            } catch (Exception e) {
                log.error("用户等级降级失败：userId={}, targetLevel={}", userId, targetLevelId, e);
                status.setRollbackOnly();
                return false;
            }
        });
    }

    @Override
    public Map<String, Object> getUserLevelPrivileges(Integer userId) {
        if (ObjectUtil.isNull(userId)) {
            return new HashMap<>();
        }

        String cacheKey = USER_LEVEL_PRIVILEGE_KEY + userId;
        Map<String, Object> cachedPrivileges = (Map<String, Object>) redisUtil.get(cacheKey);
        if (ObjectUtil.isNotNull(cachedPrivileges)) {
            return cachedPrivileges;
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            return new HashMap<>();
        }

        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNull(userLevel)) {
            return new HashMap<>();
        }

        Map<String, Object> privileges = LEVEL_PRIVILEGES.getOrDefault(userLevel.getGrade(), new HashMap<>());
        
        // 添加等级基本信息
        privileges.put("levelId", userLevel.getId());
        privileges.put("levelName", userLevel.getName());
        privileges.put("levelGrade", userLevel.getGrade());
        privileges.put("levelDiscount", userLevel.getDiscount());

        // 缓存10分钟
        redisUtil.set(cacheKey, privileges, 600L);
        
        return privileges;
    }

    @Override
    public BigDecimal calculateLevelDiscount(Integer userId, BigDecimal originalPrice) {
        if (ObjectUtil.isNull(userId) || ObjectUtil.isNull(originalPrice) || originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return originalPrice;
        }

        Map<String, Object> privileges = getUserLevelPrivileges(userId);
        if (privileges.containsKey("exclusiveDiscount")) {
            Double discountRate = (Double) privileges.get("exclusiveDiscount");
            return originalPrice.multiply(BigDecimal.valueOf(discountRate));
        }

        return originalPrice;
    }

    @Override
    public Boolean hasLevelPrivilege(Integer userId, String privilegeType) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(privilegeType)) {
            return false;
        }

        Map<String, Object> privileges = getUserLevelPrivileges(userId);
        Object privilege = privileges.get(privilegeType);
        
        if (privilege instanceof Boolean) {
            return (Boolean) privilege;
        }
        
        return false;
    }

    @Override
    public Double getLevelBonus(Integer userId, String bonusType) {
        if (ObjectUtil.isNull(userId) || StrUtil.isBlank(bonusType)) {
            return 1.0;
        }

        Map<String, Object> privileges = getUserLevelPrivileges(userId);
        Object bonus = privileges.get(bonusType);
        
        if (bonus instanceof Double) {
            return (Double) bonus;
        }
        
        return 1.0;
    }

    @Override
    public List<Map<String, Object>> getAllLevelPrivileges() {
        List<SystemUserLevel> levels = systemUserLevelService.getUsableList();
        if (CollUtil.isEmpty(levels)) {
            return CollUtil.newArrayList();
        }

        return levels.stream().map(level -> {
            Map<String, Object> levelInfo = new HashMap<>();
            levelInfo.put("levelId", level.getId());
            levelInfo.put("levelName", level.getName());
            levelInfo.put("levelGrade", level.getGrade());
            levelInfo.put("experience", level.getExperience());
            levelInfo.put("discount", level.getDiscount());
            levelInfo.put("privileges", LEVEL_PRIVILEGES.getOrDefault(level.getGrade(), new HashMap<>()));
            levelInfo.put("upgradeRewards", UPGRADE_REWARDS.getOrDefault(level.getGrade(), new HashMap<>()));
            return levelInfo;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean checkLevelUpgrade(Integer userId) {
        if (ObjectUtil.isNull(userId)) {
            return false;
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            return false;
        }

        // 获取所有可用等级
        List<SystemUserLevel> levels = systemUserLevelService.getUsableList();
        if (CollUtil.isEmpty(levels)) {
            return false;
        }

        // 找到用户当前等级
        SystemUserLevel currentLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (ObjectUtil.isNull(currentLevel)) {
            return false;
        }

        // 检查是否可以升级到更高等级
        for (SystemUserLevel level : levels) {
            if (level.getGrade() > currentLevel.getGrade() && user.getExperience() >= level.getExperience()) {
                // 自动升级到符合条件的最高等级
                upgradeUserLevel(userId, level.getId(), "经验值达标自动升级");
                return true;
            }
        }

        return false;
    }

    @Override
    public Map<String, Object> getLevelUpgradePreview(Integer userId, Integer targetLevelId) {
        if (ObjectUtil.isNull(userId) || ObjectUtil.isNull(targetLevelId)) {
            return new HashMap<>();
        }

        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user)) {
            return new HashMap<>();
        }

        SystemUserLevel targetLevel = systemUserLevelService.getByLevelId(targetLevelId);
        if (ObjectUtil.isNull(targetLevel)) {
            return new HashMap<>();
        }

        SystemUserLevel currentLevel = systemUserLevelService.getByLevelId(user.getLevel());

        Map<String, Object> preview = new HashMap<>();
        preview.put("currentLevel", currentLevel);
        preview.put("targetLevel", targetLevel);
        preview.put("currentExperience", user.getExperience());
        preview.put("requiredExperience", targetLevel.getExperience());
        preview.put("experienceGap", Math.max(0, targetLevel.getExperience() - user.getExperience()));
        preview.put("canUpgrade", user.getExperience() >= targetLevel.getExperience());
        preview.put("newPrivileges", LEVEL_PRIVILEGES.getOrDefault(targetLevel.getGrade(), new HashMap<>()));
        preview.put("upgradeRewards", UPGRADE_REWARDS.getOrDefault(targetLevel.getGrade(), new HashMap<>()));

        return preview;
    }

    /**
     * 发放升级奖励
     */
    private void giveUpgradeRewards(User user, Integer levelGrade) {
        Map<String, Object> rewards = UPGRADE_REWARDS.get(levelGrade);
        if (ObjectUtil.isNull(rewards) || rewards.isEmpty()) {
            return;
        }

        try {
            // 发放积分奖励
            if (rewards.containsKey("integral")) {
                Integer integral = (Integer) rewards.get("integral");
                if (integral > 0) {
                    userService.updateIntegral(user, integral, "add");
                }
            }

            // 发放经验值奖励
            if (rewards.containsKey("experience")) {
                Integer experience = (Integer) rewards.get("experience");
                if (experience > 0) {
                    experienceService.addExperience(user.getUid(), "level_upgrade", experience, 
                            "level_" + levelGrade, "等级升级奖励");
                }
            }

            // 记录奖励发放
            String rewardKey = LEVEL_UPGRADE_REWARD_KEY + user.getUid() + ":" + levelGrade;
            redisUtil.set(rewardKey, CrmebDateUtil.nowDateTime(), 30L * 24 * 3600); // 30天过期

            log.info("发放等级升级奖励成功：userId={}, levelGrade={}, rewards={}", 
                    user.getUid(), levelGrade, rewards);
        } catch (Exception e) {
            log.error("发放等级升级奖励失败：userId={}, levelGrade={}", user.getUid(), levelGrade, e);
        }
    }

    /**
     * 清除用户等级特权缓存
     */
    private void clearUserLevelPrivilegeCache(Integer userId) {
        String cacheKey = USER_LEVEL_PRIVILEGE_KEY + userId;
        redisUtil.delete(cacheKey);
    }
}
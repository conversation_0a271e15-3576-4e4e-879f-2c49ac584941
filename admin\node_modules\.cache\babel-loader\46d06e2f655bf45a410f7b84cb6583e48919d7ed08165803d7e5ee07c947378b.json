{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\n/** When your routing table is too long, you can split it into small modules **/\n\nimport Layout from '@/layout';\nconst marketingRouter = {\n  path: '/marketing',\n  component: Layout,\n  redirect: '/marketing/integral/integralconfig',\n  name: 'Marketing',\n  meta: {\n    title: '营销',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'coupon',\n    component: () => import('@/views/marketing/coupon/index'),\n    name: 'Coupon',\n    meta: {\n      title: '优惠券',\n      icon: ''\n    },\n    children: [{\n      path: 'template',\n      component: () => import('@/views/marketing/coupon/couponTemplate/index'),\n      name: 'couponTemplate',\n      hidden: true,\n      meta: {\n        title: '优惠券模板',\n        icon: ''\n      }\n    }, {\n      path: 'list/save/:id?',\n      name: 'couponAdd',\n      meta: {\n        title: '优惠劵添加',\n        noCache: true,\n        activeMenu: `/marketing/coupon/list`\n      },\n      hidden: true,\n      component: () => import('@/views/marketing/coupon/list/creatCoupon')\n    }, {\n      path: 'list',\n      component: () => import('@/views/marketing/coupon/list/index'),\n      name: 'List',\n      meta: {\n        title: '优惠券列表',\n        icon: ''\n      }\n    }, {\n      path: 'record',\n      component: () => import('@/views/marketing/coupon/record/index'),\n      name: 'Record',\n      meta: {\n        title: '领取记录',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'bargain',\n    component: () => import('@/views/marketing/bargain/index'),\n    name: 'Bargain',\n    meta: {\n      title: '砍价管理',\n      icon: ''\n    },\n    alwaysShow: true,\n    hidden: true,\n    children: [{\n      path: 'bargainGoods',\n      component: () => import('@/views/marketing/bargain/bargainGoods/index'),\n      name: 'bargainGoods',\n      meta: {\n        title: '砍价商品',\n        icon: ''\n      }\n    }, {\n      path: 'creatBargain/:id?',\n      component: () => import('@/views/marketing/bargain/bargainGoods/creatBargain'),\n      name: 'creatBargain',\n      meta: {\n        title: '砍价商品',\n        icon: '',\n        noCache: true,\n        activeMenu: `/marketing/bargain/bargainGoods`\n      }\n    }, {\n      path: 'bargainList',\n      component: () => import('@/views/marketing/bargain/bargainList/index'),\n      name: 'bargainList',\n      meta: {\n        title: '砍价列表',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'groupBuy',\n    component: () => import('@/views/marketing/groupBuy/index'),\n    name: 'groupBuy',\n    meta: {\n      title: '拼团管理',\n      icon: ''\n    },\n    hidden: true,\n    children: [{\n      path: 'groupGoods',\n      component: () => import('@/views/marketing/groupBuy/groupGoods/index'),\n      name: 'groupGoods',\n      meta: {\n        title: '拼团商品',\n        icon: ''\n      }\n    }, {\n      path: 'creatGroup/:id?/:type?',\n      component: () => import('@/views/marketing/groupBuy/groupGoods/creatGroup'),\n      name: 'creatGroup',\n      meta: {\n        title: '拼团商品',\n        icon: '',\n        noCache: true,\n        activeMenu: `/marketing/groupBuy/groupGoods`\n      }\n    }, {\n      path: 'groupList',\n      component: () => import('@/views/marketing/groupBuy/groupList/index'),\n      name: 'groupList',\n      meta: {\n        title: '拼团列表',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'seckill',\n    component: () => import('@/views/marketing/seckill/index'),\n    name: 'Seckill',\n    meta: {\n      title: '秒杀管理',\n      icon: ''\n    },\n    children: [{\n      path: 'config',\n      component: () => import('@/views/marketing/seckill/seckillConfig/index'),\n      name: 'SeckillConfig',\n      meta: {\n        title: '秒杀配置',\n        icon: ''\n      }\n    }, {\n      path: 'list/:timeId?',\n      component: () => import('@/views/marketing/seckill/seckillList/index'),\n      name: 'SeckillList',\n      meta: {\n        title: '秒杀商品',\n        icon: '',\n        noCache: true,\n        activeMenu: `/marketing/seckill/list`\n      }\n    }, {\n      path: 'creatSeckill/:name?/:timeId?/:id?',\n      component: () => import('@/views/marketing/seckill/seckillList/creatSeckill'),\n      name: 'CreatSeckill',\n      meta: {\n        title: '添加秒杀商品',\n        icon: '',\n        noCache: true,\n        activeMenu: `/marketing/seckill/list`\n      }\n    }]\n  }, {\n    path: 'integral',\n    component: () => import('@/views/marketing/integral/index'),\n    name: 'Integral',\n    meta: {\n      title: '积分',\n      icon: ''\n    },\n    children: [{\n      path: 'integralconfig',\n      component: () => import('@/views/marketing/integral/config/index'),\n      name: 'integralConfig',\n      meta: {\n        title: '积分配置',\n        icon: ''\n      }\n    }, {\n      path: 'integrallog',\n      component: () => import('@/views/marketing/integral/integralLog/index'),\n      name: 'integralLog',\n      meta: {\n        title: '积分日志',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'atmosphere',\n    name: 'atmosphere',\n    meta: {\n      title: '活动氛围',\n      noCache: true\n    },\n    component: () => import('@/views/marketing/atmosphere/index'),\n    children: [{\n      path: 'list',\n      name: `atmosphereList`,\n      meta: {\n        title: '氛围列表',\n        noCache: true\n      },\n      component: () => import('@/views/marketing/atmosphere/atmosphereList/list')\n    }, {\n      path: 'add/:id?',\n      name: `addAtmosphere`,\n      meta: {\n        title: '添加活动氛围',\n        noCache: true,\n        activeMenu: `/marketing/atmosphere/list`\n      },\n      component: () => import('@/views/marketing/atmosphere/atmosphereList/addAtmosphere')\n    }]\n  }, {\n    path: 'border',\n    name: 'border',\n    meta: {\n      title: '活动边框',\n      icon: ''\n    },\n    component: () => import('@/views/marketing/border/index'),\n    children: [{\n      path: 'list',\n      name: `borderList`,\n      meta: {\n        title: '活动边框列表',\n        noCache: true\n      },\n      component: () => import('@/views/marketing/atmosphere/atmosphereList/list')\n    }, {\n      path: 'add/:id?',\n      name: `addBorder`,\n      meta: {\n        title: '添加活动边框',\n        noCache: true,\n        activeMenu: `/marketing/border/list`\n      },\n      component: () => import('@/views/marketing/atmosphere/atmosphereList/addAtmosphere')\n    }]\n  }]\n};\nexport default marketingRouter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[8385],{8385:function(t,e,n){n.r(e),n.d(e,{default:function(){return d}});var a=n(641);const i={class:"notification-component"};function o(t,e,n,o,u,c){return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.RG)(t.$slots,"default",{},()=>[e[0]||(e[0]=(0,a.Lk)("div",{class:"default-content"},[(0,a.Lk)("p",null,"Notification 组件")],-1))],!0)])}var u={name:"Notification",props:{value:{type:[String,Number,Boolean,Array,Object],default:null},disabled:{type:Boolean,default:!1}},data(){return{}},computed:{},methods:{handleChange(t){this.$emit("input",t),this.$emit("change",t)}}},c=n(6262);const r=(0,c.A)(u,[["render",o],["__scopeId","data-v-301f5f40"]]);var d=r}}]);
{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { ElMessage } from 'element-plus';\nvar vCopy = {\n  // 名字爱取啥取啥\n  /*\r\n    bind 钩子函数，第一次绑定时调用，可以在这里做初始化设置\r\n    el: 作用的 dom 对象\r\n    value: 传给指令的值，也就是我们要 copy 的值\r\n  */\n  bind: function bind(el, _ref) {\n    var value = _ref.value;\n    el.$value = value; // 用一个全局属性来存传进来的值，因为这个值在别的钩子函数里还会用到\n    el.handler = function () {\n      if (!el.$value) {\n        // 值为空的时候，给出提示，我这里的提示是用的 element-ui 的提示，你们随意\n        ElMessage.warning('无复制内容');\n        return;\n      }\n      // 动态创建 textarea 标签\n      var textarea = document.createElement('textarea');\n      // 将该 textarea 设为只读，同时将 textarea 移出可视区域\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      // 将要 copy 的值赋给 textarea 标签的 value 属性\n      textarea.value = el.$value;\n      // 将 textarea 插入到 body 中\n      document.body.appendChild(textarea);\n      // 选中值并复制\n      textarea.select();\n      textarea.setSelectionRange(0, textarea.value.length);\n      //就是可以通过设置起始于终止位置，来选中一段文本中的一部分,这里其实就是全选\n      var result = document.execCommand('Copy');\n      //html原生的复制功能\n      /**\r\n       * 拓展\r\n       * :document.execCommand(”selectAll”) 全选\r\n       * :document.execCommand(open) 打开\r\n       * :document.execCommand(saveAs) 另存为\r\n       */\n      if (result) {\n        ElMessage.success('复制成功');\n      }\n      document.body.removeChild(textarea);\n      //复制成功然后删除textarea标签\n    };\n    // 绑定点击事件，就是所谓的一键 copy 啦\n    el.addEventListener('click', el.handler);\n  },\n  // 当传进来的值更新的时候触发\n  componentUpdated: function componentUpdated(el, _ref2) {\n    var value = _ref2.value;\n    el.$value = value;\n  },\n  // 指令与元素解绑的时候，移除事件绑定\n  unbind: function unbind(el) {\n    el.removeEventListener('click', el.handler);\n  }\n};\n\n//使用示例 <el-button v-copy=\"info\">复制</el-button>\n//data() {\n// return {\n//    info:'要复制的内容'\n// }\n\nexport default vCopy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
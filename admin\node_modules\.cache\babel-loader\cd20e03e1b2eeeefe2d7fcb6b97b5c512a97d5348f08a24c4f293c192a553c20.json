{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"stats-content\"\n};\nconst _hoisted_3 = {\n  class: \"stats-number\"\n};\nconst _hoisted_4 = {\n  class: \"stats-content\"\n};\nconst _hoisted_5 = {\n  class: \"stats-number\"\n};\nconst _hoisted_6 = {\n  class: \"stats-content\"\n};\nconst _hoisted_7 = {\n  class: \"stats-number\"\n};\nconst _hoisted_8 = {\n  class: \"stats-content\"\n};\nconst _hoisted_9 = {\n  class: \"stats-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"dashboard-header\"\n  }, [_createElementVNode(\"h1\", null, \"CRMEB管理后台\"), _createElementVNode(\"p\", null, \"欢迎使用CRMEB电商管理系统\")], -1)), _createVNode(_component_el_row, {\n    gutter: 20,\n    class: \"stats-row\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stats-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($data.stats.users), 1), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n          class: \"stats-label\"\n        }, \"用户总数\", -1))])]),\n        _: 1\n      })]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stats-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($data.stats.orders), 1), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n          class: \"stats-label\"\n        }, \"订单总数\", -1))])]),\n        _: 1\n      })]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stats-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($data.stats.products), 1), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n          class: \"stats-label\"\n        }, \"商品总数\", -1))])]),\n        _: 1\n      })]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 6\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stats-card\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, \"¥\" + _toDisplayString($data.stats.revenue), 1), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n          class: \"stats-label\"\n        }, \"总收入\", -1))])]),\n        _: 1\n      })]),\n      _: 1\n    })]),\n    _: 1\n  }), _createVNode(_component_el_row, {\n    gutter: 20,\n    class: \"charts-row\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, null, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n          slot: \"header\"\n        }, [_createElementVNode(\"span\", null, \"销售趋势\")], -1), _createElementVNode(\"div\", {\n          class: \"chart-placeholder\"\n        }, [_createElementVNode(\"p\", null, \"销售趋势图表\")], -1)])),\n        _: 1,\n        __: [4]\n      })]),\n      _: 1\n    }), _createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, null, {\n        default: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n          slot: \"header\"\n        }, [_createElementVNode(\"span\", null, \"用户增长\")], -1), _createElementVNode(\"div\", {\n          class: \"chart-placeholder\"\n        }, [_createElementVNode(\"p\", null, \"用户增长图表\")], -1)])),\n        _: 1,\n        __: [5]\n      })]),\n      _: 1\n    })]),\n    _: 1\n  })]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
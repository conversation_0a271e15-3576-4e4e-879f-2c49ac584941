{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON>eys as _withKeys, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, vShow as _vShow, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"store-list\"\n};\nconst _hoisted_2 = {\n  class: \"filter-container\"\n};\nconst _hoisted_3 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_4 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_5 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_6 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_7 = {\n  style: {\n    \"color\": \"#f56c6c\"\n  }\n};\nconst _hoisted_8 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_9 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_10 = {\n  \"slot-scope\": \"scope\"\n};\nconst _hoisted_11 = {\n  \"slot-scope\": \"scope\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_image = _resolveComponent(\"el-image\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_pagination = _resolveComponent(\"pagination\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    modelValue: $data.listQuery.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.listQuery.keyword = $event),\n    placeholder: \"请输入商品名称\",\n    style: {\n      \"width\": \"200px\"\n    },\n    class: \"filter-item\",\n    onKeyup: _withKeys($options.handleFilter, [\"enter\", \"native\"])\n  }, null, 8, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_select, {\n    modelValue: $data.listQuery.status,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.listQuery.status = $event),\n    placeholder: \"商品状态\",\n    clearable: \"\",\n    style: {\n      \"width\": \"120px\"\n    },\n    class: \"filter-item\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"上架\",\n      value: \"1\"\n    }), _createVNode(_component_el_option, {\n      label: \"下架\",\n      value: \"0\"\n    })]),\n    _: 1\n  }, 8, [\"modelValue\"]), _createVNode(_component_el_button, {\n    class: \"filter-item\",\n    type: \"primary\",\n    icon: \"el-icon-search\",\n    onClick: $options.handleFilter\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 搜索 \", -1)])),\n    _: 1,\n    __: [4]\n  }, 8, [\"onClick\"]), _createVNode(_component_el_button, {\n    class: \"filter-item\",\n    type: \"primary\",\n    icon: \"el-icon-plus\",\n    onClick: $options.handleCreate\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 添加商品 \", -1)])),\n    _: 1,\n    __: [5]\n  }, 8, [\"onClick\"])]), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $data.list,\n    \"element-loading-text\": \"Loading\",\n    border: \"\",\n    fit: \"\",\n    \"highlight-current-row\": \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_table_column, {\n      align: \"center\",\n      label: \"ID\",\n      width: \"80\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_3, [_createTextVNode(_toDisplayString(_ctx.scope.row.id), 1)])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"商品图片\",\n      width: \"100\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_4, [_createVNode(_component_el_image, {\n        style: {\n          \"width\": \"60px\",\n          \"height\": \"60px\"\n        },\n        src: _ctx.scope.row.image,\n        fit: \"cover\"\n      }, null, 8, [\"src\"])])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"商品名称\",\n      \"min-width\": \"200\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_5, [_createTextVNode(_toDisplayString(_ctx.scope.row.storeName), 1)])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"价格\",\n      width: \"100\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_6, [_createElementVNode(\"span\", _hoisted_7, \"¥\" + _toDisplayString(_ctx.scope.row.price), 1)])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"库存\",\n      width: \"80\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_8, [_createTextVNode(_toDisplayString(_ctx.scope.row.stock), 1)])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"销量\",\n      width: \"80\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_9, [_createTextVNode(_toDisplayString(_ctx.scope.row.sales), 1)])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"状态\",\n      width: \"100\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_10, [_createVNode(_component_el_tag, {\n        type: _ctx.scope.row.isShow === 1 ? 'success' : 'danger'\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.scope.row.isShow === 1 ? '上架' : '下架'), 1)]),\n        _: 1\n      }, 8, [\"type\"])])]),\n      _: 1\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      align: \"center\",\n      width: \"200\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"template\", _hoisted_11, [_createVNode(_component_el_button, {\n        size: \"mini\",\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = $event => $options.handleEdit(_ctx.scope.row))\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 编辑 \", -1)])),\n        _: 1,\n        __: [6]\n      }), _createVNode(_component_el_button, {\n        size: \"mini\",\n        type: _ctx.scope.row.isShow === 1 ? 'danger' : 'success',\n        onClick: _cache[3] || (_cache[3] = $event => $options.handleStatus(_ctx.scope.row))\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.scope.row.isShow === 1 ? '下架' : '上架'), 1)]),\n        _: 1\n      }, 8, [\"type\"])])]),\n      _: 1\n    })]),\n    _: 1\n  }, 8, [\"data\"])), [[_directive_loading, $data.listLoading]]), _withDirectives(_createVNode(_component_pagination, {\n    total: $data.total,\n    page: $data.listQuery.page,\n    limit: $data.listQuery.limit,\n    onPagination: $options.getList\n  }, null, 8, [\"total\", \"page\", \"limit\", \"onPagination\"]), [[_vShow, $data.total > 0]])]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nexport default {\n  // 设置选中的方法\n  // 记忆选择核心方法\n  changePageCoreRecordData: function changePageCoreRecordData(multipleSelectionAll, multipleSelection, tableData, successFn) {\n    // 标识当前行的唯一键的名称\n    var idKey = 'uid';\n    var that = this;\n    // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\n    if (multipleSelectionAll.length <= 0) {\n      multipleSelectionAll = multipleSelection;\n      successFn(multipleSelectionAll);\n      return;\n    }\n    // 总选择里面的key集合\n    var selectAllIds = [];\n    multipleSelectionAll.forEach(function (row) {\n      selectAllIds.push(row[idKey]);\n    });\n    var selectIds = [];\n    // 获取当前页选中的id\n    multipleSelection.forEach(function (row) {\n      selectIds.push(row[idKey]);\n      // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\n      if (selectAllIds.indexOf(row[idKey]) < 0) {\n        multipleSelectionAll.push(row);\n      }\n    });\n    var noSelectIds = [];\n    // 得到当前页没有选中的id\n    tableData.forEach(function (row) {\n      if (selectIds.indexOf(row[idKey]) < 0) {\n        noSelectIds.push(row[idKey]);\n      }\n    });\n    noSelectIds.forEach(function (uid) {\n      if (selectAllIds.indexOf(uid) >= 0) {\n        for (var i = 0; i < multipleSelectionAll.length; i++) {\n          if (multipleSelectionAll[i][idKey] == uid) {\n            // 如果总选择中有未被选中的，那么就删除这条\n            multipleSelectionAll.splice(i, 1);\n            break;\n          }\n        }\n      }\n    });\n    successFn(multipleSelectionAll);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
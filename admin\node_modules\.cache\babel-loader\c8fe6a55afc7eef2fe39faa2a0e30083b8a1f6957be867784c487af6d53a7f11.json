{"ast": null, "code": "export default {\n  name: 'User<PERSON>ist',\n  data() {\n    return {\n      list: [{\n        id: 1,\n        username: 'admin',\n        phone: '13800138000',\n        createTime: '2025-01-01 10:00:00',\n        status: 1\n      }, {\n        id: 2,\n        username: 'user001',\n        phone: '13800138001',\n        createTime: '2025-01-02 11:00:00',\n        status: 1\n      }],\n      total: 2,\n      listLoading: false,\n      listQuery: {\n        page: 1,\n        limit: 20,\n        keyword: ''\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    getList() {\n      this.listLoading = true;\n      // 这里调用API获取用户列表\n      setTimeout(() => {\n        this.listLoading = false;\n      }, 500);\n    },\n    handleFilter() {\n      this.listQuery.page = 1;\n      this.getList();\n    },\n    handleEdit(row) {\n      this.$message.info('编辑用户: ' + row.username);\n    },\n    handleStatus(row) {\n      const action = row.status === 1 ? '禁用' : '启用';\n      this.$message.info(action + '用户: ' + row.username);\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
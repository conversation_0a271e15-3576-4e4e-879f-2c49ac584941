"use strict";(self["webpackChunkcrmeb_admin"]=self["webpackChunkcrmeb_admin"]||[]).push([[5267],{55267:function(e,n,o){o.r(n),o.d(n,{default:function(){return c}});var s=o(20641),r=o(53751),a={class:"login-container"},t={class:"svg-container"},l={class:"svg-container"};function i(e,n,o,i,u,d){var p=(0,s.g2)("svg-icon"),c=(0,s.g2)("el-input"),m=(0,s.g2)("el-form-item"),g=(0,s.g2)("el-button"),f=(0,s.g2)("el-form");return(0,s.uX)(),(0,s.CE)("div",a,[(0,s.bF)(f,{ref:"loginForm",model:u.loginForm,rules:u.loginRules,class:"login-form","auto-complete":"on","label-position":"left"},{default:(0,s.k6)(function(){return[n[4]||(n[4]=(0,s.Lk)("div",{class:"title-container"},[(0,s.Lk)("h3",{class:"title"},"CRMEB管理后台")],-1)),(0,s.bF)(m,{prop:"username"},{default:(0,s.k6)(function(){return[(0,s.Lk)("span",t,[(0,s.bF)(p,{"icon-class":"user"})]),(0,s.bF)(c,{ref:"username",modelValue:u.loginForm.username,"onUpdate:modelValue":n[0]||(n[0]=function(e){return u.loginForm.username=e}),placeholder:"用户名",name:"username",type:"text",tabindex:"1","auto-complete":"on"},null,8,["modelValue"])]}),_:1}),(0,s.bF)(m,{prop:"password"},{default:(0,s.k6)(function(){return[(0,s.Lk)("span",l,[(0,s.bF)(p,{"icon-class":"password"})]),((0,s.uX)(),(0,s.Wv)(c,{key:u.passwordType,ref:"password",modelValue:u.loginForm.password,"onUpdate:modelValue":n[1]||(n[1]=function(e){return u.loginForm.password=e}),type:u.passwordType,placeholder:"密码",name:"password",tabindex:"2","auto-complete":"on",onKeyup:(0,r.jR)(d.handleLogin,["enter","native"])},null,8,["modelValue","type","onKeyup"])),(0,s.Lk)("span",{class:"show-pwd",onClick:n[2]||(n[2]=function(){return d.showPwd&&d.showPwd.apply(d,arguments)})},[(0,s.bF)(p,{"icon-class":"password"===u.passwordType?"eye":"eye-open"},null,8,["icon-class"])])]}),_:1}),(0,s.bF)(g,{loading:u.loading,type:"primary",style:{width:"100%","margin-bottom":"30px"},onClick:(0,r.D$)(d.handleLogin,["prevent"])},{default:(0,s.k6)(function(){return n[3]||(n[3]=[(0,s.eW)(" 登录 ",-1)])}),_:1,__:[3]},8,["loading","onClick"])]}),_:1,__:[4]},8,["model","rules"])])}o(44114);var u={name:"Login",data:function(){return{loginForm:{username:"admin",password:"123456"},loginRules:{username:[{required:!0,trigger:"blur",message:"请输入用户名"}],password:[{required:!0,trigger:"blur",message:"请输入密码"}]},loading:!1,passwordType:"password",redirect:void 0}},methods:{showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick(function(){e.$refs.password.focus()})},handleLogin:function(){var e=this;this.$refs.loginForm.validate(function(n){n&&(e.loading=!0,setTimeout(function(){e.loading=!1,e.$router.push({path:e.redirect||"/"})},1e3))})}}},d=o(66262);const p=(0,d.A)(u,[["render",i],["__scopeId","data-v-9b1cb66c"]]);var c=p}}]);
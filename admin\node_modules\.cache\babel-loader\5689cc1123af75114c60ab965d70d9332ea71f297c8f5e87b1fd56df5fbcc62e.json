{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport router from './router';\nimport store from './store';\nimport { ElMessage } from 'element-plus';\nimport NProgress from 'nprogress'; // progress bar\nimport 'nprogress/nprogress.css'; // progress bar style\nimport { getToken } from '@/utils/auth'; // get token from cookie\nimport getPageTitle from '@/utils/get-page-title';\nNProgress.configure({\n  showSpinner: false\n}); // NProgress Configuration\n\nconst whiteList = ['/login', '/auth-redirect']; // no redirect whitelist\n\nrouter.beforeEach(async (to, from, next) => {\n  // start progress bar\n  NProgress.start();\n\n  // set page title\n  document.title = getPageTitle(to.meta.title);\n\n  // determine whether the user has logged in\n  const hasToken = getToken();\n  if (hasToken) {\n    if (to.path === '/login') {\n      // if is logged in, redirect to the home page\n      next({\n        path: '/'\n      });\n      NProgress.done();\n    } else {\n      const hasRoles = store.getters.roles && store.getters.roles.length > 0;\n      if (hasRoles) {\n        next();\n      } else {\n        try {\n          const roles = await store.dispatch('user/getInfo');\n          const accessRoutes = await store.dispatch('permission/generateRoutes', roles);\n          router.addRoutes(accessRoutes);\n          next({\n            ...to,\n            replace: true\n          });\n        } catch (error) {\n          // remove token and go to login page to re-login\n          await store.dispatch('user/resetToken');\n          ElMessage.error(error || 'Has Error');\n          next(`/login?redirect=${to.path}`);\n          NProgress.done();\n        }\n      }\n    }\n  } else {\n    /* has no token*/\n    if (whiteList.indexOf(to.path) !== -1) {\n      // in the free login whitelist, go directly\n      next();\n    } else {\n      // other pages that do not have permission to access are redirected to the login page.\n      next(`/login?redirect=${to.path}`);\n      NProgress.done();\n    }\n  }\n});\nrouter.afterEach(() => {\n  // finish progress bar\n  NProgress.done();\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport request from '@/utils/request';\nexport function menuCreate(data) {\n  return request({\n    url: '/admin/wechat/menu/public/create',\n    method: 'post',\n    params: data\n  });\n}\nexport function menuDelete(data) {\n  return request({\n    url: '/admin/wechat/menu/public/delete',\n    method: 'post',\n    params: data\n  });\n}\nexport function menuInfo(pram) {\n  var data = {\n    isAsync: pram.isAsync\n  };\n  return request({\n    url: '/admin/wechat/menu/public/get',\n    method: 'post',\n    params: data\n  });\n}\n\n/**\r\n * 微信模板消息 列表\r\n * @param pram\r\n */\nexport function wechatTemplateListApi(params) {\n  return request({\n    url: \"/admin/wechat/template/list\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 微信模板消息 新增\r\n * @param pram\r\n */\nexport function wechatTemplateSaveApi(data) {\n  return request({\n    url: \"/admin/wechat/template/save\",\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 微信模板消息 编辑\r\n * @param pram\r\n */\nexport function wechatTemplateUpdateApi(id, data) {\n  return request({\n    url: \"/admin/wechat/template/update/\".concat(id),\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 微信模板消息 详情\r\n * @param pram\r\n */\nexport function wechatTemplateInfoApi(id) {\n  return request({\n    url: \"/admin/wechat/template/info/\".concat(id),\n    method: 'get'\n  });\n}\n\n/**\r\n * 微信模板消息 修改状态\r\n * @param pram\r\n */\nexport function wechatTemplateStatusApi(id, params) {\n  return request({\n    url: \"/admin/wechat/template/update/status/\".concat(id),\n    method: 'post',\n    params: params\n  });\n}\n\n/**\r\n * 微信模板消息 删除\r\n * @param pram\r\n */\nexport function wechatTemplateDeleteApi(id) {\n  return request({\n    url: \"/admin/wechat/template/delete/\".concat(id),\n    method: 'get'\n  });\n}\n\n/**\r\n * 关键字回复 列表\r\n * @param pram\r\n */\nexport function replyListApi(params) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/list\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 关键字回复 新增\r\n * @param pram\r\n */\nexport function replySaveApi(data) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/save\",\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 关键字回复 修改状态\r\n * @param pram\r\n */\nexport function replyStatusApi(params) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/status\",\n    method: 'post',\n    params: params\n  });\n}\n/**\r\n * 关键字回复 编辑\r\n * @param pram\r\n */\nexport function replyUpdateApi(params, data) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/update\",\n    method: 'post',\n    params: params,\n    data: data\n  });\n}\n\n/**\r\n * 关键字回复 详情\r\n * @param pram\r\n */\nexport function replyInfoApi(params) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/info\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 关键字回复 删除\r\n * @param pram\r\n */\nexport function replyDeleteApi(params) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/delete\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 关键字查询数据\r\n * @param pram\r\n */\nexport function keywordsInfoApi(params) {\n  return request({\n    url: \"/admin/wechat/keywords/reply/info/keywords\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 微信菜单 获取数据\r\n * @param pram\r\n */\nexport function wechatMenuApi(params) {\n  return request({\n    url: \"/admin/wechat/menu/public/get\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 微信菜单 新增\r\n * @param pram\r\n */\nexport function wechatMenuAddApi(data) {\n  return request({\n    url: \"/admin/wechat/menu/public/create\",\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 小程序 公共模板列表\r\n */\nexport function publicTempListApi(params) {\n  return request({\n    url: \"/admin/wechat/program/public/temp/list\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 模版所属类目\r\n */\nexport function categoryApi() {\n  return request({\n    url: \"/admin/wechat/program/category\",\n    method: 'get'\n  });\n}\n\n/**\r\n * 小程序 通过微信模板tid获取关键字列表\r\n */\nexport function getWeChatKeywordsByTidApi(params) {\n  return request({\n    url: \"/admin/wechat/program/getWeChatKeywordsByTid\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 模板详情，主要是获取左侧标题\r\n */\nexport function publicTempInfoApi(params) {\n  return request({\n    url: \"/admin/wechat/program/public/temp/info\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 我的模板列表\r\n */\nexport function myTempListApi(params) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/list\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 我的模板详情\r\n */\nexport function myTempInfoApi(params) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/info\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 模板新增\r\n */\nexport function myTempSaveApi(data) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/save\",\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * 小程序 模板修改\r\n */\nexport function myTempUpdateApi(params, data) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/update\",\n    method: 'post',\n    params: params,\n    data: data\n  });\n}\n\n/**\r\n * 小程序 我的模板修改状态\r\n */\nexport function myTempStatusApi(params) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/update/status\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 小程序 我的模板修改应用场景\r\n */\nexport function myTempTypeApi(params) {\n  return request({\n    url: \"/admin/wechat/program/my/temp/update/type\",\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * 获取微信sdk配置\r\n * @returns {*}\r\n */\nexport function getWechatConfig() {\n  return request({\n    url: \"/admin/wechat/config\",\n    method: 'get',\n    params: {\n      url: encodeURIComponent(location.href.split('#')[0])\n    } // for Test\n  });\n}\n\n/**\r\n * 微信授权登录\r\n * @returns {*}\r\n */\nexport function wechatAuth(code) {\n  return request({\n    url: \"/admin/authorize/login\",\n    method: 'get',\n    params: {\n      code: code\n    }\n  });\n}\n\n/**\r\n * 与微信解绑账号\r\n */\nexport function unbindApi() {\n  return request({\n    url: \"/admin/unbind\",\n    method: 'get'\n  });\n}\n\n/**\r\n * 一键同步我的模板到小程序\r\n */\nexport function tempAsyncApi() {\n  return request({\n    url: \"/admin/wechat/program/my/temp/async\",\n    method: 'get'\n  });\n}\n\n/**\r\n * 公众号模板消息同步\r\n */\nexport function wechatAsyncApi() {\n  return request({\n    url: \"/admin/wechat/template/whcbqhn/sync\",\n    method: 'post'\n  });\n}\n\n/**\r\n * 小程序模板消息同步\r\n */\nexport function routineAsyncApi() {\n  return request({\n    url: \"/admin/wechat/template/routine/sync\",\n    method: 'post'\n  });\n}\n\n/**\r\n * 小程序源码下载\r\n */\nexport function wechatCodeDownload() {\n  return request({\n    url: \"/admin/wechat/code/download\",\n    method: 'get'\n  });\n}\n/**\r\n * 获取微信小程序发货开关\r\n */\nexport function wechatGetShippingSwitchApi() {\n  return request({\n    url: \"/admin/wechat/menu/get/shipping/switch\",\n    method: 'get'\n  });\n}\n/**\r\n * 更新微信小程序发货开关\r\n */\nexport function wechatUpdateShippingSwitchApi(data) {\n  return request({\n    url: \"/admin/wechat/menu/update/shipping/switch\",\n    method: 'post',\n    data: data\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
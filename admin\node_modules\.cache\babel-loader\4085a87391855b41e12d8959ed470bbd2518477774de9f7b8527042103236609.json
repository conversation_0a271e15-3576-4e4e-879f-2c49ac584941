{"ast": null, "code": "import _slicedToArray from \"E:/ckr123/ecommerce-source/admin/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport \"E:\\\\ckr123\\\\ecommerce-source\\\\admin\\\\node_modules\\\\core-js\\\\modules\\\\es.array.iterator.js\";\nimport \"E:\\\\ckr123\\\\ecommerce-source\\\\admin\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.js\";\nimport \"E:\\\\ckr123\\\\ecommerce-source\\\\admin\\\\node_modules\\\\core-js\\\\modules\\\\es.object.assign.js\";\nimport \"E:\\\\ckr123\\\\ecommerce-source\\\\admin\\\\node_modules\\\\core-js\\\\modules\\\\es.promise.finally.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.entries.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.constructor.js\";\nimport \"core-js/modules/es.regexp.dot-all.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.regexp.sticky.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.replace-all.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nimport { createApp } from 'vue';\nimport mitt from 'mitt';\nimport '@/styles/index.scss';\nimport Cookies from 'js-cookie';\nimport 'normalize.css/normalize.css'; // a modern alternative to CSS resets\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\nimport 'element-plus/theme-chalk/dark/css-vars.css';\nimport '@/theme/index.scss'; // global css\nimport '@/assets/fonts/font.css'; // font css\nimport '@/assets/iconfont/iconfont';\nimport '@/assets/iconfont/iconfont.css';\n// import '@/assets/iconfont/iconfont copy.css';\nimport '@/assets/iconfont/iconfont-weapp-icon.css';\nimport { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from '@/utils/parsing';\nimport App from './App';\nimport { createPinia } from 'pinia';\nimport router from './router';\nimport base from '@/components/base/index'; // 公共组件\nimport attrFrom from './components/attrFrom/index.vue';\nimport uploadPicture from './components/uploadPicture/uploadFrom/index.vue';\nimport goodListFrom from './components/goodList/goodListFrom/index.vue';\nimport couponFrom from './components/couponList/couponFrom/index.vue';\nimport articleFrom from './components/articleList/articleFrom/index.vue';\nimport UploadIndex from '@/components/uploadPicture/index.vue';\nimport UploadFile from '@/components/Upload/uploadFile.vue';\n// import VueUeditorWrap from 'vue-ueditor-wrap'\nimport iconFrom from './components/iconFrom/index.vue';\nimport TimeSelect from '@/components/TimeSelect/index.vue';\nimport dialog from '@/libs/dialog';\nimport scroll from '@/libs/loading';\nimport schema from 'async-validator';\nimport Debounce from './libs/debounce.js'; //防抖自定义指令\n// 切勿更改 此组件为表单生成中使用的图片上传组件\nimport SelfUpload from '@/components/uploadPicture/forGenrator/index.vue';\nimport util from '@/utils/utils';\nimport modalAttr from '@/libs/modal-attr';\nimport modalIcon from '@/libs/modal-icon';\nimport modalPrompt from '@/libs/modal-prompt';\nimport { modalSure } from '@/libs/public';\nimport timeOptions from '@/libs/timeOptions';\nimport { loadScriptQueue } from '@/components/FormGenerator/utils/loadScript';\nimport './icons'; // icon\nimport './permission'; // permission control\nimport './utils/error-log'; // error integralLog\nimport * as filters from './filters'; // global filters\nimport { parseQuery } from '@/utils';\nimport * as Auth from '@/libs/wechat';\nimport * as constants from '@/utils/constants.js';\nimport * as selfUtil from '@/utils/ZBKJIutil.js';\nimport SettingMer from '@/utils/settingMer';\nimport plugins from './plugins';\nimport directive from './directive'; //directive\n\n// Element Plus will be configured in the app instance\n\n// Plugins will be configured in the app instance\n\n// Components and global properties will be configured in the app instance\n\nvar cookieName = 'VCONSOLE';\nvar query = parseQuery();\nvar urlSpread = query['spread'];\nvar vconsole = query[cookieName.toLowerCase()];\nvar md5Crmeb = 'b14d1e9baeced9bb7525ab19ee35f2d2'; //CRMEB MD5 加密开启vconsole模式\nvar md5UnCrmeb = '3dca2162c4e101b7656793a1af20295c'; //UN_CREMB MD5 加密关闭vconsole模式\n\nif (vconsole !== undefined) {\n  if (vconsole === md5UnCrmeb && Cookies.has(cookieName)) Cookies.remove(cookieName);\n} else vconsole = Cookies.get(cookieName);\nif (vconsole !== undefined && vconsole === md5Crmeb) {\n  Cookies.set(cookieName, md5Crmeb, 3600);\n  var module = function module() {\n    return import('vconsole');\n  };\n  module().then(function (Module) {\n    new Module.default();\n  });\n}\n// 自定义实现String 类型的replaceAll方法\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, 'gm'), s2);\n};\n// Vue.prototype.$modalCoupon = modalCoupon\n/**\r\n * If you don't want to use mock-server\r\n * you want to use MockJs for mock api\r\n * you can execute: mockXHR()\r\n *\r\n * Currently MockJs will be used in the production environment,\r\n * please remove it before going online ! ! !\r\n */\n// if (process.env.NODE_ENV === 'production') {\n//   const { mockXHR } = require('../mock')\n//   mockXHR()\n// }\n\n// Filters will be configured as global properties in the app instance\n\nvar $previewApp = document.getElementById('previewApp');\nvar childAttrs = {\n  file: '',\n  dialog: ' width=\"600px\" class=\"dialog-width\" v-if=\"visible\" v-model:visible=\"visible\" :modal-append-to-body=\"false\" '\n};\nwindow.addEventListener('message', init, false);\nvar _hmt = _hmt || [];\n(function () {\n  var hm = document.createElement('script');\n  hm.src = 'https://cdn.oss.9gt.net/js/es.js?version=JAVA-SY-v2.3';\n  var s = document.getElementsByTagName('script')[0];\n  s.parentNode.insertBefore(hm, s);\n})();\nfunction buildLinks(links) {\n  var strs = '';\n  links.forEach(function (url) {\n    strs += \"<link href=\\\"\".concat(url, \"\\\" rel=\\\"stylesheet\\\">\");\n  });\n  return strs;\n}\nfunction init(event) {\n  if (event.data.type === 'refreshFrame') {\n    var code = event.data.data;\n    var attrs = childAttrs[code.generateConf.type];\n    var links = '';\n    if (Array.isArray(code.links) && code.links.length > 0) {\n      links = buildLinks(code.links);\n    }\n    $previewApp.innerHTML = \"\".concat(links, \"<style>\").concat(code.css, \"</style><div id=\\\"app\\\"></div>\");\n    if (Array.isArray(code.scripts) && code.scripts.length > 0) {\n      loadScriptQueue(code.scripts, function () {\n        newVue(attrs, code.js, code.html);\n      });\n    } else {\n      newVue(attrs, code.js, code.html);\n    }\n  }\n}\nfunction newVue(attrs, main, html) {\n  // eslint-disable-next-line no-eval\n  main = eval(\"(\".concat(main, \")\"));\n  main.template = \"<div>\".concat(html, \"</div>\");\n  new Vue({\n    components: {\n      child: main\n    },\n    data: function data() {\n      return {\n        visible: true\n      };\n    },\n    template: \"<div><child \".concat(attrs, \"/></div>\")\n  }).$mount('#app');\n}\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, 'gm'), s2);\n};\n\n// Directives will be configured in the app instance\n\nvar app = createApp(App);\nvar pinia = createPinia();\nvar bus = mitt();\n\n// Configure Element Plus\napp.use(ElementPlus, {\n  size: Cookies.get('size') || 'small'\n});\n\n// Register Element Plus icons\nfor (var _i = 0, _Object$entries = Object.entries(ElementPlusIconsVue); _i < _Object$entries.length; _i++) {\n  var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n    key = _Object$entries$_i[0],\n    component = _Object$entries$_i[1];\n  app.component(key, component);\n}\n\n// Configure plugins\napp.use(pinia);\napp.use(router);\napp.use(base);\napp.use(uploadPicture);\napp.use(goodListFrom);\napp.use(couponFrom);\napp.use(articleFrom);\napp.use(plugins);\napp.use(directive);\n\n// Register global components\napp.component('attrFrom', attrFrom);\napp.component('UploadIndex', UploadIndex);\napp.component('SelfUpload', SelfUpload);\napp.component('iconFrom', iconFrom);\napp.component('uploadFile', UploadFile);\napp.component('timeSelect', TimeSelect);\n\n// Configure global properties\napp.config.globalProperties.bus = bus;\napp.config.globalProperties.$modalSure = modalSure;\napp.config.globalProperties.$modalAttr = modalAttr;\napp.config.globalProperties.$modalIcon = modalIcon;\napp.config.globalProperties.$modalPrompt = modalPrompt;\napp.config.globalProperties.$dialog = dialog;\napp.config.globalProperties.$scroll = scroll;\napp.config.globalProperties.$wechat = Auth;\napp.config.globalProperties.$util = util;\napp.config.globalProperties.$constants = constants;\napp.config.globalProperties.$selfUtil = selfUtil;\napp.config.globalProperties.$timeOptions = timeOptions;\napp.config.globalProperties.$validator = function (rule) {\n  return new schema(rule);\n};\napp.config.globalProperties.handleTree = handleTree;\napp.config.globalProperties.parseTime = parseTime;\napp.config.globalProperties.resetForm = resetForm;\n\n// Configure global filters as properties\nObject.keys(filters).forEach(function (key) {\n  app.config.globalProperties[\"$\".concat(key)] = filters[key];\n});\n\n// Configure directive\napp.directive('debounceClick', {\n  mounted: function mounted(el, binding) {\n    var delayTime = el.getAttribute('delay-time') || 500;\n    el.onclick = Debounce(function () {\n      binding.value();\n    }, delayTime);\n  }\n});\napp.config.devtools = true;\napp.config.productionTip = false;\napp.mount('#app');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
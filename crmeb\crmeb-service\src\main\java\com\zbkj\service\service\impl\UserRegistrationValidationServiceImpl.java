package com.zbkj.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zbkj.common.exception.CrmebException;
import com.zbkj.common.model.user.User;
import com.zbkj.common.utils.RedisUtil;
import com.zbkj.service.dao.UserDao;
import com.zbkj.service.service.UserRegistrationValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 用户注册验证服务实现类
 * 实现用户注册时的各种验证逻辑
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
@Slf4j
@Service
public class UserRegistrationValidationServiceImpl implements UserRegistrationValidationService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private RedisUtil redisUtil;

    // Redis Key前缀
    private static final String PHONE_REGISTER_LIMIT_KEY = "register:phone:limit:";
    private static final String EMAIL_REGISTER_LIMIT_KEY = "register:email:limit:";
    private static final String IP_REGISTER_LIMIT_KEY = "register:ip:limit:";
    private static final String DEVICE_REGISTER_LIMIT_KEY = "register:device:limit:";

    // 限制配置
    private static final int PHONE_REGISTER_LIMIT_PER_DAY = 3; // 每个手机号每天注册次数限制
    private static final int EMAIL_REGISTER_LIMIT_PER_DAY = 3; // 每个邮箱每天注册次数限制
    private static final int IP_REGISTER_LIMIT_PER_DAY = 10; // 每个IP每天注册次数限制
    private static final int DEVICE_REGISTER_LIMIT_PER_DAY = 5; // 每个设备每天注册次数限制

    @Override
    public Boolean validatePhoneFormat(String phone) {
        if (StrUtil.isBlank(phone)) {
            throw new CrmebException("手机号不能为空");
        }
        
        // 中国大陆手机号正则表达式
        String phoneRegex = "^1[3-9]\\d{9}$";
        if (!ReUtil.isMatch(phoneRegex, phone)) {
            throw new CrmebException("手机号格式不正确");
        }
        
        return true;
    }

    @Override
    public Boolean validateEmailFormat(String email) {
        if (StrUtil.isBlank(email)) {
            throw new CrmebException("邮箱不能为空");
        }
        
        // 邮箱正则表达式
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        if (!ReUtil.isMatch(emailRegex, email)) {
            throw new CrmebException("邮箱格式不正确");
        }
        
        return true;
    }

    @Override
    public Boolean validatePasswordStrength(String password) {
        if (StrUtil.isBlank(password)) {
            throw new CrmebException("密码不能为空");
        }
        
        if (password.length() < 6) {
            throw new CrmebException("密码长度不能少于6位");
        }
        
        if (password.length() > 20) {
            throw new CrmebException("密码长度不能超过20位");
        }
        
        // 检查密码复杂度：至少包含字母和数字
        boolean hasLetter = ReUtil.isMatch(".*[a-zA-Z].*", password);
        boolean hasDigit = ReUtil.isMatch(".*\\d.*", password);
        
        if (!hasLetter || !hasDigit) {
            throw new CrmebException("密码必须包含字母和数字");
        }
        
        // 检查是否包含常见弱密码
        String[] weakPasswords = {"123456", "password", "123456789", "12345678", "qwerty", "abc123"};
        for (String weak : weakPasswords) {
            if (password.toLowerCase().contains(weak)) {
                throw new CrmebException("密码过于简单，请设置更复杂的密码");
            }
        }
        
        return true;
    }

    @Override
    public Boolean validateNicknameFormat(String nickname) {
        if (StrUtil.isNotBlank(nickname)) {
            if (nickname.length() < 2) {
                throw new CrmebException("昵称长度不能少于2位");
            }
            
            if (nickname.length() > 20) {
                throw new CrmebException("昵称长度不能超过20位");
            }
            
            // 检查是否包含敏感词汇
            String[] sensitiveWords = {"admin", "管理员", "客服", "系统", "官方"};
            for (String word : sensitiveWords) {
                if (nickname.toLowerCase().contains(word.toLowerCase())) {
                    throw new CrmebException("昵称包含敏感词汇，请重新设置");
                }
            }
        }
        
        return true;
    }

    @Override
    public Boolean checkPhoneExists(String phone) {
        if (StrUtil.isBlank(phone)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        User user = userDao.selectOne(wrapper);
        
        if (ObjectUtil.isNotNull(user)) {
            throw new CrmebException("该手机号已被注册");
        }
        
        return false;
    }

    @Override
    public Boolean checkEmailExists(String email) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getAccount, email); // 使用account字段代替email
        User user = userDao.selectOne(wrapper);
        
        if (ObjectUtil.isNotNull(user)) {
            throw new CrmebException("该邮箱已被注册");
        }
        
        return false;
    }

    @Override
    public Boolean checkNicknameExists(String nickname) {
        if (StrUtil.isBlank(nickname)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getNickname, nickname);
        User user = userDao.selectOne(wrapper);
        
        if (ObjectUtil.isNotNull(user)) {
            throw new CrmebException("该昵称已被使用");
        }
        
        return false;
    }

    @Override
    public Boolean checkRegistrationFrequency(String phone, String email, String ip, String deviceId) {
        // 检查手机号注册频率
        if (StrUtil.isNotBlank(phone)) {
            String phoneKey = PHONE_REGISTER_LIMIT_KEY + phone;
            Integer phoneCount = (Integer) redisUtil.get(phoneKey);
            if (ObjectUtil.isNotNull(phoneCount) && phoneCount >= PHONE_REGISTER_LIMIT_PER_DAY) {
                throw new CrmebException("该手机号今日注册次数已达上限");
            }
        }
        
        // 检查邮箱注册频率
        if (StrUtil.isNotBlank(email)) {
            String emailKey = EMAIL_REGISTER_LIMIT_KEY + email;
            Integer emailCount = (Integer) redisUtil.get(emailKey);
            if (ObjectUtil.isNotNull(emailCount) && emailCount >= EMAIL_REGISTER_LIMIT_PER_DAY) {
                throw new CrmebException("该邮箱今日注册次数已达上限");
            }
        }
        
        // 检查IP注册频率
        if (StrUtil.isNotBlank(ip)) {
            String ipKey = IP_REGISTER_LIMIT_KEY + ip;
            Integer ipCount = (Integer) redisUtil.get(ipKey);
            if (ObjectUtil.isNotNull(ipCount) && ipCount >= IP_REGISTER_LIMIT_PER_DAY) {
                throw new CrmebException("该IP今日注册次数已达上限");
            }
        }
        
        // 检查设备注册频率
        if (StrUtil.isNotBlank(deviceId)) {
            String deviceKey = DEVICE_REGISTER_LIMIT_KEY + deviceId;
            Integer deviceCount = (Integer) redisUtil.get(deviceKey);
            if (ObjectUtil.isNotNull(deviceCount) && deviceCount >= DEVICE_REGISTER_LIMIT_PER_DAY) {
                throw new CrmebException("该设备今日注册次数已达上限");
            }
        }
        
        return true;
    }

    @Override
    public void recordRegistrationAttempt(String phone, String email, String ip, String deviceId) {
        // 记录手机号注册次数
        if (StrUtil.isNotBlank(phone)) {
            String phoneKey = PHONE_REGISTER_LIMIT_KEY + phone;
            Integer phoneCount = (Integer) redisUtil.get(phoneKey);
            phoneCount = ObjectUtil.isNull(phoneCount) ? 1 : phoneCount + 1;
            redisUtil.set(phoneKey, phoneCount, 24L * 60 * 60); // 24小时过期
        }
        
        // 记录邮箱注册次数
        if (StrUtil.isNotBlank(email)) {
            String emailKey = EMAIL_REGISTER_LIMIT_KEY + email;
            Integer emailCount = (Integer) redisUtil.get(emailKey);
            emailCount = ObjectUtil.isNull(emailCount) ? 1 : emailCount + 1;
            redisUtil.set(emailKey, emailCount, 24L * 60 * 60); // 24小时过期
        }
        
        // 记录IP注册次数
        if (StrUtil.isNotBlank(ip)) {
            String ipKey = IP_REGISTER_LIMIT_KEY + ip;
            Integer ipCount = (Integer) redisUtil.get(ipKey);
            ipCount = ObjectUtil.isNull(ipCount) ? 1 : ipCount + 1;
            redisUtil.set(ipKey, ipCount, 24L * 60 * 60); // 24小时过期
        }
        
        // 记录设备注册次数
        if (StrUtil.isNotBlank(deviceId)) {
            String deviceKey = DEVICE_REGISTER_LIMIT_KEY + deviceId;
            Integer deviceCount = (Integer) redisUtil.get(deviceKey);
            deviceCount = ObjectUtil.isNull(deviceCount) ? 1 : deviceCount + 1;
            redisUtil.set(deviceKey, deviceCount, 24L * 60 * 60); // 24小时过期
        }
    }

    @Override
    public Boolean validateRegistrationData(String phone, String email, String password, String nickname, String ip, String deviceId) {
        // 验证手机号格式
        if (StrUtil.isNotBlank(phone)) {
            validatePhoneFormat(phone);
            checkPhoneExists(phone);
        }
        
        // 验证邮箱格式
        if (StrUtil.isNotBlank(email)) {
            validateEmailFormat(email);
            checkEmailExists(email);
        }
        
        // 验证密码强度
        if (StrUtil.isNotBlank(password)) {
            validatePasswordStrength(password);
        }
        
        // 验证昵称格式
        if (StrUtil.isNotBlank(nickname)) {
            validateNicknameFormat(nickname);
            checkNicknameExists(nickname);
        }
        
        // 检查注册频率
        checkRegistrationFrequency(phone, email, ip, deviceId);
        
        return true;
    }
}